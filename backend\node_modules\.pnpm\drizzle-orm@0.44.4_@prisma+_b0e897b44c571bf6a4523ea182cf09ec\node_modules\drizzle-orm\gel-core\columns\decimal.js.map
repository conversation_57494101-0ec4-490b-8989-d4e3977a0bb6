{"version": 3, "sources": ["../../../src/gel-core/columns/decimal.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelDecimalBuilderInitial<TName extends string> = GelDecimalBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'GelDecimal';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class GelDecimalBuilder<T extends ColumnBuilderBaseConfig<'string', 'GelDecimal'>> extends GelColumnBuilder<\n\tT\n> {\n\tstatic override readonly [entityKind]: string = 'GelDecimalBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'GelDecimal');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelDecimal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelDecimal<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelDecimal<T extends ColumnBaseConfig<'string', 'GelDecimal'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelDecimal';\n\n\tconstructor(table: AnyGelTable<{ name: T['tableName'] }>, config: GelDecimalBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'numeric';\n\t}\n}\n\nexport function decimal(): GelDecimalBuilderInitial<''>;\nexport function decimal<TName extends string>(name: TName): GelDecimalBuilderInitial<TName>;\nexport function decimal(name?: string) {\n\treturn new GelDecimalBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,WAAW,wBAAwB;AAWrC,MAAM,0BAAqF,iBAEhG;AAAA,EACD,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,YAAY;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBAAuE,UAAa;AAAA,EAChG,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,OAA8C,QAAwC;AACjG,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,QAAQ,MAAe;AACtC,SAAO,IAAI,kBAAkB,QAAQ,EAAE;AACxC;", "names": []}