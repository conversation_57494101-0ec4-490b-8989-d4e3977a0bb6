{"version": 3, "sources": ["../../src/bun-sqlite/session.ts"], "sourcesContent": ["/// <reference types=\"bun-types\" />\n\nimport type { Database, Statement as BunStatement } from 'bun:sqlite';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport type { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport { SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport type {\n\tPreparedQueryConfig as PreparedQueryConfigBase,\n\tSQLiteExecuteMethod,\n\tSQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { SQLitePreparedQuery as PreparedQueryBase, SQLiteSession } from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport interface SQLiteBunSessionOptions {\n\tlogger?: Logger;\n}\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\ntype Statement = BunStatement<any>;\n\nexport class SQLiteBunSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteSession<'sync', void, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBunSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: Database,\n\t\tdialect: SQLiteSyncDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: SQLiteBunSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\texec(query: string): void {\n\t\tthis.client.exec(query);\n\t}\n\n\tprepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t): PreparedQuery<T> {\n\t\tconst stmt = this.client.prepare(query.sql);\n\t\treturn new PreparedQuery(\n\t\t\tstmt,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: SQLiteBunTransaction<TFullSchema, TSchema>) => T,\n\t\tconfig: SQLiteTransactionConfig = {},\n\t): T {\n\t\tconst tx = new SQLiteBunTransaction('sync', this.dialect, this, this.schema);\n\t\tlet result: T | undefined;\n\t\tconst nativeTx = this.client.transaction(() => {\n\t\t\tresult = transaction(tx);\n\t\t});\n\t\tnativeTx[config.behavior ?? 'deferred']();\n\t\treturn result!;\n\t}\n}\n\nexport class SQLiteBunTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteTransaction<'sync', void, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBunTransaction';\n\n\toverride transaction<T>(transaction: (tx: SQLiteBunTransaction<TFullSchema, TSchema>) => T): T {\n\t\tconst savepointName = `sp${this.nestedIndex}`;\n\t\tconst tx = new SQLiteBunTransaction('sync', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tthis.session.run(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = transaction(tx);\n\t\t\tthis.session.run(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tthis.session.run(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class PreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends PreparedQueryBase<\n\t{ type: 'sync'; run: void; all: T['all']; get: T['get']; values: T['values']; execute: T['execute'] }\n> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBunPreparedQuery';\n\n\tconstructor(\n\t\tprivate stmt: Statement,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => unknown,\n\t) {\n\t\tsuper('sync', executeMethod, query);\n\t}\n\n\trun(placeholderValues?: Record<string, unknown>) {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.stmt.run(...params);\n\t}\n\n\tall(placeholderValues?: Record<string, unknown>): T['all'] {\n\t\tconst { fields, query, logger, joinsNotNullableMap, stmt, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\t\t\treturn stmt.all(...params);\n\t\t}\n\n\t\tconst rows = this.values(placeholderValues) as unknown[][];\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows) as T['all'];\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow(fields!, row, joinsNotNullableMap));\n\t}\n\n\tget(placeholderValues?: Record<string, unknown>): T['get'] {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\tconst row = this.stmt.values(...params)[0];\n\n\t\tif (!row) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tconst { fields, joinsNotNullableMap, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn row;\n\t\t}\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper([row]) as T['get'];\n\t\t}\n\n\t\treturn mapResultRow(fields!, row, joinsNotNullableMap);\n\t}\n\n\tvalues(placeholderValues?: Record<string, unknown>): T['values'] {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.stmt.values(...params);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n"], "mappings": "AAGA,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAE3B,SAAS,kBAA8B,WAAW;AAElD,SAAS,yBAAyB;AAOlC,SAAS,uBAAuB,mBAAmB,qBAAqB;AACxE,SAAS,oBAAoB;AAStB,MAAM,yBAGH,cAAkD;AAAA,EAK3D,YACS,QACR,SACQ,QACR,UAAmC,CAAC,GACnC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAAA,EAChD;AAAA,EAZA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAYR,KAAK,OAAqB;AACzB,SAAK,OAAO,KAAK,KAAK;AAAA,EACvB;AAAA,EAEA,aACC,OACA,QACA,eACA,uBACA,oBACmB;AACnB,UAAM,OAAO,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC1C,WAAO,IAAI;AAAA,MACV;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,YACR,aACA,SAAkC,CAAC,GAC/B;AACJ,UAAM,KAAK,IAAI,qBAAqB,QAAQ,KAAK,SAAS,MAAM,KAAK,MAAM;AAC3E,QAAI;AACJ,UAAM,WAAW,KAAK,OAAO,YAAY,MAAM;AAC9C,eAAS,YAAY,EAAE;AAAA,IACxB,CAAC;AACD,aAAS,OAAO,YAAY,UAAU,EAAE;AACxC,WAAO;AAAA,EACR;AACD;AAEO,MAAM,6BAGH,kBAAsD;AAAA,EAC/D,QAA0B,UAAU,IAAY;AAAA,EAEvC,YAAe,aAAuE;AAC9F,UAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,UAAM,KAAK,IAAI,qBAAqB,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AACzG,SAAK,QAAQ,IAAI,IAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,QAAQ,IAAI,IAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,WAAK,QAAQ,IAAI,IAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,sBAA2E,kBAEtF;AAAA,EAGD,YACS,MACR,OACQ,QACA,QACR,eACQ,wBACA,oBACP;AACD,UAAM,QAAQ,eAAe,KAAK;AAR1B;AAEA;AACA;AAEA;AACA;AAAA,EAGT;AAAA,EAZA,QAA0B,UAAU,IAAY;AAAA,EAchD,IAAI,mBAA6C;AAChD,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,KAAK,IAAI,GAAG,MAAM;AAAA,EAC/B;AAAA,EAEA,IAAI,mBAAuD;AAC1D,UAAM,EAAE,QAAQ,OAAO,QAAQ,qBAAqB,MAAM,mBAAmB,IAAI;AACjF,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,SAAS,iBAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AACjC,aAAO,KAAK,IAAI,GAAG,MAAM;AAAA,IAC1B;AAEA,UAAM,OAAO,KAAK,OAAO,iBAAiB;AAE1C,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,QAAQ,aAAa,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACzE;AAAA,EAEA,IAAI,mBAAuD;AAC1D,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,UAAM,MAAM,KAAK,KAAK,OAAO,GAAG,MAAM,EAAE,CAAC;AAEzC,QAAI,CAAC,KAAK;AACT,aAAO;AAAA,IACR;AAEA,UAAM,EAAE,QAAQ,qBAAqB,mBAAmB,IAAI;AAC5D,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO;AAAA,IACR;AAEA,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,CAAC,GAAG,CAAC;AAAA,IAChC;AAEA,WAAO,aAAa,QAAS,KAAK,mBAAmB;AAAA,EACtD;AAAA,EAEA,OAAO,mBAA0D;AAChE,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,KAAK,OAAO,GAAG,MAAM;AAAA,EAClC;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}