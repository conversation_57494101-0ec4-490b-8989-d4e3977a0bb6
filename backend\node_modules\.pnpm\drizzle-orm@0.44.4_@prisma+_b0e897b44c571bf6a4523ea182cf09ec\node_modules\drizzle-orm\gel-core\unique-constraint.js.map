{"version": 3, "sources": ["../../src/gel-core/unique-constraint.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { GelColumn } from './columns/index.ts';\nimport type { GelTable } from './table.ts';\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport function uniqueKeyName(table: GelTable, columns: string[]) {\n\treturn `${table[TableName]}_${columns.join('_')}_unique`;\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'GelUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: GelColumn[];\n\t/** @internal */\n\tnullsNotDistinctConfig = false;\n\n\tconstructor(\n\t\tcolumns: GelColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\tnullsNotDistinct() {\n\t\tthis.nullsNotDistinctConfig = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: GelTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.nullsNotDistinctConfig, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'GelUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [GelColumn, ...GelColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'GelUniqueConstraint';\n\n\treadonly columns: GelColumn[];\n\treadonly name?: string;\n\treadonly nullsNotDistinct: boolean = false;\n\n\tconstructor(readonly table: GelTable, columns: GelColumn[], nullsNotDistinct: boolean, name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t\tthis.nullsNotDistinct = nullsNotDistinct;\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAInB,SAAS,OAAO,MAA0C;AAChE,SAAO,IAAI,0BAA0B,IAAI;AAC1C;AAEO,SAAS,cAAc,OAAiB,SAAmB;AACjE,SAAO,GAAG,MAAM,SAAS,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC;AAChD;AAEO,MAAM,wBAAwB;AAAA,EAQpC,YACC,SACQ,MACP;AADO;AAER,SAAK,UAAU;AAAA,EAChB;AAAA,EAZA,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAEA,yBAAyB;AAAA,EASzB,mBAAmB;AAClB,SAAK,yBAAyB;AAC9B,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,OAAmC;AACxC,WAAO,IAAI,iBAAiB,OAAO,KAAK,SAAS,KAAK,wBAAwB,KAAK,IAAI;AAAA,EACxF;AACD;AAEO,MAAM,0BAA0B;AAAA,EACtC,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAEA,YACC,MACC;AACD,SAAK,OAAO;AAAA,EACb;AAAA,EAEA,MAAM,SAAsC;AAC3C,WAAO,IAAI,wBAAwB,SAAS,KAAK,IAAI;AAAA,EACtD;AACD;AAEO,MAAM,iBAAiB;AAAA,EAO7B,YAAqB,OAAiB,SAAsB,kBAA2B,MAAe;AAAjF;AACpB,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ,cAAc,KAAK,OAAO,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC;AACvF,SAAK,mBAAmB;AAAA,EACzB;AAAA,EAVA,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EACA,mBAA4B;AAAA,EAQrC,UAAU;AACT,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}