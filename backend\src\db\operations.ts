import { eq, and, desc, count } from 'drizzle-orm';
import type { Database } from './index';
import { users, products, productVersions, authorizations, licenses } from './schema';
import type { NewUser, NewProduct, NewProductVersion, NewAuthorization, NewLicense } from './schema';

// 用户操作
export class UserOperations {
  constructor(private db: Database) {}

  // 创建用户
  async createUser(userData: NewUser) {
    const [user] = await this.db.insert(users).values({
      ...userData,
      updatedAt: new Date().toISOString()
    }).returning();
    return user;
  }

  // 根据用户名查找用户
  async findUserByUsername(username: string) {
    const [user] = await this.db
      .select()
      .from(users)
      .where(eq(users.username, username))
      .limit(1);
    return user;
  }

  // 获取活跃的分销商列表
  async getActiveDistributors() {
    return await this.db
      .select()
      .from(users)
      .where(and(
        eq(users.role, 'DISTRIBUTOR'),
        eq(users.status, 'ACTIVE')
      ))
      .orderBy(desc(users.createdAt));
  }
}

// 产品操作
export class ProductOperations {
  constructor(private db: Database) {}

  // 创建产品
  async createProduct(productData: NewProduct) {
    const [product] = await this.db.insert(products).values({
      ...productData,
      updatedAt: new Date().toISOString()
    }).returning();
    return product;
  }

  // 获取活跃产品列表
  async getActiveProducts() {
    return await this.db
      .select()
      .from(products)
      .where(eq(products.status, 'ACTIVE'))
      .orderBy(desc(products.createdAt));
  }

  // 获取产品及其版本
  async getProductWithVersions(productId: number) {
    const product = await this.db
      .select()
      .from(products)
      .where(eq(products.id, productId))
      .limit(1);

    if (!product[0]) return null;

    const versions = await this.db
      .select()
      .from(productVersions)
      .where(and(
        eq(productVersions.productId, productId),
        eq(productVersions.status, 'ACTIVE')
      ))
      .orderBy(desc(productVersions.createdAt));

    return {
      ...product[0],
      versions
    };
  }
}

// 产品版本操作
export class ProductVersionOperations {
  constructor(private db: Database) {}

  // 创建产品版本
  async createVersion(versionData: NewProductVersion) {
    const [version] = await this.db.insert(productVersions).values({
      ...versionData,
      updatedAt: new Date().toISOString()
    }).returning();
    return version;
  }

  // 获取版本详情
  async getVersionById(versionId: number) {
    const [version] = await this.db
      .select()
      .from(productVersions)
      .where(eq(productVersions.id, versionId))
      .limit(1);
    return version;
  }
}

// 授权操作
export class AuthorizationOperations {
  constructor(private db: Database) {}

  // 创建授权
  async createAuthorization(authData: NewAuthorization) {
    const [authorization] = await this.db.insert(authorizations).values({
      ...authData,
      updatedAt: new Date().toISOString()
    }).returning();
    return authorization;
  }

  // 检查分销商是否有权限销售某个版本
  async checkDistributorAuthorization(distributorId: number, versionId: number) {
    const [auth] = await this.db
      .select()
      .from(authorizations)
      .where(and(
        eq(authorizations.distributorId, distributorId),
        eq(authorizations.versionId, versionId),
        eq(authorizations.status, 'ACTIVE')
      ))
      .limit(1);
    return auth;
  }

  // 获取分销商的所有授权
  async getDistributorAuthorizations(distributorId: number) {
    return await this.db
      .select({
        authorization: authorizations,
        product: products,
        version: productVersions
      })
      .from(authorizations)
      .innerJoin(productVersions, eq(authorizations.versionId, productVersions.id))
      .innerJoin(products, eq(productVersions.productId, products.id))
      .where(and(
        eq(authorizations.distributorId, distributorId),
        eq(authorizations.status, 'ACTIVE')
      ))
      .orderBy(desc(authorizations.createdAt));
  }
}

// 许可证操作
export class LicenseOperations {
  constructor(private db: Database) {}

  // 创建许可证
  async createLicense(licenseData: NewLicense) {
    const [license] = await this.db.insert(licenses).values({
      ...licenseData,
      updatedAt: new Date().toISOString()
    }).returning();
    return license;
  }

  // 根据许可证密钥查找许可证
  async findLicenseByKey(licenseKey: string) {
    const [license] = await this.db
      .select({
        license: licenses,
        version: productVersions,
        product: products
      })
      .from(licenses)
      .innerJoin(productVersions, eq(licenses.versionId, productVersions.id))
      .innerJoin(products, eq(productVersions.productId, products.id))
      .where(eq(licenses.licenseKey, licenseKey))
      .limit(1);
    return license;
  }

  // 激活许可证
  async activateLicense(licenseKey: string, verifyConfig?: string) {
    const [license] = await this.db
      .update(licenses)
      .set({
        status: 'ACTIVE',
        activatedAt: new Date().toISOString(),
        verifyConfig,
        updatedAt: new Date().toISOString()
      })
      .where(eq(licenses.licenseKey, licenseKey))
      .returning();
    return license;
  }

  // 获取分销商的许可证统计
  async getDistributorLicenseStats(distributorId: number) {
    const stats = await this.db
      .select({
        status: licenses.status,
        count: count()
      })
      .from(licenses)
      .where(eq(licenses.distributorId, distributorId))
      .groupBy(licenses.status);
    
    return stats.reduce((acc, stat) => {
      acc[stat.status] = stat.count;
      return acc;
    }, {} as Record<string, number>);
  }
}

// 导出操作类的工厂函数
export function createOperations(db: Database) {
  return {
    users: new UserOperations(db),
    products: new ProductOperations(db),
    versions: new ProductVersionOperations(db),
    authorizations: new AuthorizationOperations(db),
    licenses: new LicenseOperations(db)
  };
}
