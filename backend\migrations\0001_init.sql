-- CreateTable
CREATE TABLE "users" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "username" TEXT NOT NULL,
    "passwordHash" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "nickName" TEXT,
    "wechat" TEXT,
    "avatar" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "products" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "product_versions" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "productId" INTEGER NOT NULL,
    "version" TEXT NOT NULL,
    "versionName" TEXT,
    "description" TEXT,
    "configTemplate" TEXT NOT NULL,
    "encryptionKey" TEXT NOT NULL,
    "defaultPrice" REAL NOT NULL,
    "downloadLink" TEXT,
    "coverUrl" TEXT,
    "changelog" TEXT,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "product_versions_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "authorizations" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "distributorId" INTEGER NOT NULL,
    "versionId" INTEGER NOT NULL,
    "customPrice" REAL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "authorizations_distributorId_fkey" FOREIGN KEY ("distributorId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "authorizations_versionId_fkey" FOREIGN KEY ("versionId") REFERENCES "product_versions" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "licenses" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "versionId" INTEGER NOT NULL,
    "licenseKey" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'INACTIVE',
    "verifyConfig" TEXT,
    "activatedAt" DATETIME,
    "distributorId" INTEGER NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "licenses_versionId_fkey" FOREIGN KEY ("versionId") REFERENCES "product_versions" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "licenses_distributorId_fkey" FOREIGN KEY ("distributorId") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- CreateIndex
CREATE INDEX "users_role_idx" ON "users"("role");

-- CreateIndex
CREATE INDEX "users_status_idx" ON "users"("status");

-- CreateIndex
CREATE INDEX "users_createdAt_idx" ON "users"("createdAt");

-- CreateIndex
CREATE INDEX "users_role_status_idx" ON "users"("role", "status");

-- CreateIndex
CREATE UNIQUE INDEX "products_name_key" ON "products"("name");

-- CreateIndex
CREATE INDEX "products_status_idx" ON "products"("status");

-- CreateIndex
CREATE INDEX "products_category_idx" ON "products"("category");

-- CreateIndex
CREATE INDEX "products_createdAt_idx" ON "products"("createdAt");

-- CreateIndex
CREATE INDEX "product_versions_productId_idx" ON "product_versions"("productId");

-- CreateIndex
CREATE INDEX "product_versions_status_idx" ON "product_versions"("status");

-- CreateIndex
CREATE INDEX "product_versions_createdAt_idx" ON "product_versions"("createdAt");

-- CreateIndex
CREATE INDEX "product_versions_productId_status_idx" ON "product_versions"("productId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "product_versions_productId_version_key" ON "product_versions"("productId", "version");

-- CreateIndex
CREATE INDEX "authorizations_distributorId_idx" ON "authorizations"("distributorId");

-- CreateIndex
CREATE INDEX "authorizations_versionId_idx" ON "authorizations"("versionId");

-- CreateIndex
CREATE INDEX "authorizations_status_idx" ON "authorizations"("status");

-- CreateIndex
CREATE INDEX "authorizations_createdAt_idx" ON "authorizations"("createdAt");

-- CreateIndex
CREATE INDEX "authorizations_distributorId_status_idx" ON "authorizations"("distributorId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "authorizations_distributorId_versionId_key" ON "authorizations"("distributorId", "versionId");

-- CreateIndex
CREATE UNIQUE INDEX "licenses_licenseKey_key" ON "licenses"("licenseKey");

-- CreateIndex
CREATE INDEX "licenses_licenseKey_idx" ON "licenses"("licenseKey");

-- CreateIndex
CREATE INDEX "licenses_versionId_idx" ON "licenses"("versionId");

-- CreateIndex
CREATE INDEX "licenses_distributorId_idx" ON "licenses"("distributorId");

-- CreateIndex
CREATE INDEX "licenses_status_idx" ON "licenses"("status");

-- CreateIndex
CREATE INDEX "licenses_activatedAt_idx" ON "licenses"("activatedAt");

-- CreateIndex
CREATE INDEX "licenses_createdAt_idx" ON "licenses"("createdAt");

-- CreateIndex
CREATE INDEX "licenses_distributorId_status_idx" ON "licenses"("distributorId", "status");

-- CreateIndex
CREATE INDEX "licenses_status_createdAt_idx" ON "licenses"("status", "createdAt");
