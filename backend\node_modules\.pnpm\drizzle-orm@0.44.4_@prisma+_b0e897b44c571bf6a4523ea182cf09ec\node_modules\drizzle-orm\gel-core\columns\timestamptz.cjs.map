{"version": 3, "sources": ["../../../src/gel-core/columns/timestamptz.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn } from './common.ts';\nimport { GelLocalDateColumnBaseBuilder } from './date.common.ts';\n\nexport type GelTimestampTzBuilderInitial<TName extends string> = GelTimestampTzBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'GelTimestampTz';\n\tdata: Date;\n\tdriverParam: Date;\n\tenumValues: undefined;\n}>;\n\nexport class GelTimestampTzBuilder<T extends ColumnBuilderBaseConfig<'date', 'GelTimestampTz'>>\n\textends GelLocalDateColumnBaseBuilder<\n\t\tT\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'GelTimestampTzBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t) {\n\t\tsuper(name, 'date', 'GelTimestampTz');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelTimestampTz<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelTimestampTz<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelTimestampTz<T extends ColumnBaseConfig<'date', 'GelTimestampTz'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelTimestampTz';\n\n\tconstructor(table: AnyGelTable<{ name: T['tableName'] }>, config: GelTimestampTzBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'datetime';\n\t}\n}\n\nexport function timestamptz(): GelTimestampTzBuilderInitial<''>;\nexport function timestamptz<TName extends string>(\n\tname: TName,\n): GelTimestampTzBuilderInitial<TName>;\nexport function timestamptz(name?: string) {\n\treturn new GelTimestampTzBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0B;AAC1B,yBAA8C;AAWvC,MAAM,8BACJ,iDAGT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YACC,MACC;AACD,UAAM,MAAM,QAAQ,gBAAgB;AAAA,EACrC;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBAA6E,wBAAa;AAAA,EACtG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,OAA8C,QAA4C;AACrG,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAMO,SAAS,YAAY,MAAe;AAC1C,SAAO,IAAI,sBAAsB,QAAQ,EAAE;AAC5C;", "names": []}