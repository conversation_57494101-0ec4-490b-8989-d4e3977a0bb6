{"version": 3, "sources": ["../../../src/gel-core/query-builders/query.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport {\n\ttype BuildQueryResult,\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tmapRelationalRow,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, QueryWithTypings, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { KnownKeysOnly } from '~/utils.ts';\nimport type { GelDialect } from '../dialect.ts';\nimport type { GelPreparedQuery, GelSession, PreparedQueryConfig } from '../session.ts';\nimport type { GelTable } from '../table.ts';\n\nexport class RelationalQueryBuilder<TSchema extends TablesRelationalConfig, T<PERSON><PERSON><PERSON> extends TableRelationalConfig> {\n\tstatic readonly [entityKind]: string = 'GelRelationalQueryBuilder';\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TSchema,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: GelTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: GelDialect,\n\t\tprivate session: GelSession,\n\t) {}\n\n\tfindMany<TConfig extends DBQueryConfig<'many', true, TSchema, TFields>>(\n\t\tconfig?: KnownKeysOnly<TConfig, DBQueryConfig<'many', true, TSchema, TFields>>,\n\t): GelRelationalQuery<BuildQueryResult<TSchema, TFields, TConfig>[]> {\n\t\treturn new GelRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? (config as DBQueryConfig<'many', true>) : {},\n\t\t\t'many',\n\t\t);\n\t}\n\n\tfindFirst<TSelection extends Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>(\n\t\tconfig?: KnownKeysOnly<TSelection, Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>,\n\t): GelRelationalQuery<BuildQueryResult<TSchema, TFields, TSelection> | undefined> {\n\t\treturn new GelRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? { ...(config as DBQueryConfig<'many', true> | undefined), limit: 1 } : { limit: 1 },\n\t\t\t'first',\n\t\t);\n\t}\n}\n\nexport class GelRelationalQuery<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'gel'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'GelRelationalQuery';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'gel';\n\t\treadonly result: TResult;\n\t};\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TablesRelationalConfig,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: GelTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: GelDialect,\n\t\tprivate session: GelSession,\n\t\tprivate config: DBQueryConfig<'many', true> | true,\n\t\tprivate mode: 'many' | 'first',\n\t) {\n\t\tsuper();\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): GelPreparedQuery<PreparedQueryConfig & { execute: TResult }> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\tconst { query, builtQuery } = this._toSQL();\n\n\t\t\treturn this.session.prepareQuery<PreparedQueryConfig & { execute: TResult }>(\n\t\t\t\tbuiltQuery,\n\t\t\t\tundefined,\n\t\t\t\tname,\n\t\t\t\ttrue,\n\t\t\t\t(rawRows, mapColumnValue) => {\n\t\t\t\t\tconst rows = rawRows.map((row) =>\n\t\t\t\t\t\tmapRelationalRow(this.schema, this.tableConfig, row, query.selection, mapColumnValue)\n\t\t\t\t\t);\n\t\t\t\t\tif (this.mode === 'first') {\n\t\t\t\t\t\treturn rows[0] as TResult;\n\t\t\t\t\t}\n\t\t\t\t\treturn rows as TResult;\n\t\t\t\t},\n\t\t\t);\n\t\t});\n\t}\n\n\tprepare(name: string): GelPreparedQuery<PreparedQueryConfig & { execute: TResult }> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate _getQuery() {\n\t\treturn this.dialect.buildRelationalQueryWithoutPK({\n\t\t\tfullSchema: this.fullSchema,\n\t\t\tschema: this.schema,\n\t\t\ttableNamesMap: this.tableNamesMap,\n\t\t\ttable: this.table,\n\t\t\ttableConfig: this.tableConfig,\n\t\t\tqueryConfig: this.config,\n\t\t\ttableAlias: this.tableConfig.tsName,\n\t\t});\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this._getQuery().sql as SQL;\n\t}\n\n\tprivate _toSQL(): { query: BuildRelationalQueryResult; builtQuery: QueryWithTypings } {\n\t\tconst query = this._getQuery();\n\n\t\tconst builtQuery = this.dialect.sqlToQuery(query.sql as SQL);\n\n\t\treturn { query, builtQuery };\n\t}\n\n\ttoSQL(): Query {\n\t\treturn this._toSQL().builtQuery;\n\t}\n\n\toverride execute(): Promise<TResult> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(undefined);\n\t\t});\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B;AAAA,EAIC;AAAA,OAGM;AAGP,SAAS,cAAc;AAMhB,MAAM,uBAAsG;AAAA,EAGlH,YACS,YACA,QACA,eACA,OACA,aACA,SACA,SACP;AAPO;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACN;AAAA,EAVH,QAAiB,UAAU,IAAY;AAAA,EAYvC,SACC,QACoE;AACpE,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAU,SAAyC,CAAC;AAAA,MACpD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,UACC,QACiF;AACjF,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS,EAAE,GAAI,QAAoD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;AAAA,MAC3F;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,2BAAoC,aAEjD;AAAA,EAQC,YACS,YACA,QACA,eACA,OACA,aACA,SACA,SACA,QACA,MACP;AACD,UAAM;AAVE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EAGT;AAAA,EAnBA,QAA0B,UAAU,IAAY;AAAA;AAAA,EAsBhD,SAAS,MAA6E;AACrF,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,YAAM,EAAE,OAAO,WAAW,IAAI,KAAK,OAAO;AAE1C,aAAO,KAAK,QAAQ;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC,SAAS,mBAAmB;AAC5B,gBAAM,OAAO,QAAQ;AAAA,YAAI,CAAC,QACzB,iBAAiB,KAAK,QAAQ,KAAK,aAAa,KAAK,MAAM,WAAW,cAAc;AAAA,UACrF;AACA,cAAI,KAAK,SAAS,SAAS;AAC1B,mBAAO,KAAK,CAAC;AAAA,UACd;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,QAAQ,MAA4E;AACnF,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAEQ,YAAY;AACnB,WAAO,KAAK,QAAQ,8BAA8B;AAAA,MACjD,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,eAAe,KAAK;AAAA,MACpB,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,YAAY,KAAK,YAAY;AAAA,IAC9B,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,UAAU,EAAE;AAAA,EACzB;AAAA,EAEQ,SAA8E;AACrF,UAAM,QAAQ,KAAK,UAAU;AAE7B,UAAM,aAAa,KAAK,QAAQ,WAAW,MAAM,GAAU;AAE3D,WAAO,EAAE,OAAO,WAAW;AAAA,EAC5B;AAAA,EAEA,QAAe;AACd,WAAO,KAAK,OAAO,EAAE;AAAA,EACtB;AAAA,EAES,UAA4B;AACpC,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,MAAS;AAAA,IACzC,CAAC;AAAA,EACF;AACD;", "names": []}