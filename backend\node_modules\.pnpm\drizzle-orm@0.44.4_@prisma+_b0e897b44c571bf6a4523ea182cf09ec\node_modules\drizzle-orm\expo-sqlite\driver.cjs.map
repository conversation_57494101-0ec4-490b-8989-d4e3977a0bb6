{"version": 3, "sources": ["../../src/expo-sqlite/driver.ts"], "sourcesContent": ["import type { SQLiteDatabase, SQLiteRunResult } from 'expo-sqlite';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { ExpoSQLiteSession } from './session.ts';\n\nexport class ExpoSQLiteDatabase<TSchema extends Record<string, unknown> = Record<string, never>>\n\textends BaseSQLiteDatabase<'sync', SQLiteRunResult, TSchema>\n{\n\tstatic override readonly [entityKind]: string = 'ExpoSQLiteDatabase';\n}\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: SQLiteDatabase,\n\tconfig: DrizzleConfig<TSchema> = {},\n): ExpoSQLiteDatabase<TSchema> & {\n\t$client: SQLiteDatabase;\n} {\n\tconst dialect = new SQLiteSyncDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new ExpoSQLiteSession(client, dialect, schema, { logger });\n\tconst db = new ExpoSQLiteDatabase('sync', dialect, session, schema) as ExpoSQLiteDatabase<TSchema>;\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAC3B,oBAA8B;AAC9B,uBAKO;AACP,gBAAmC;AACnC,qBAAkC;AAElC,qBAAkC;AAE3B,MAAM,2BACJ,6BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AACjD;AAEO,SAAS,QACf,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,iCAAkB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC/D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,iCAAkB,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AACzE,QAAM,KAAK,IAAI,mBAAmB,QAAQ,SAAS,SAAS,MAAM;AAClE,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;", "names": []}