{"version": 3, "sources": ["../../src/expo-sqlite/migrator.ts"], "sourcesContent": ["import { useEffect, useReducer } from 'react';\nimport type { MigrationMeta } from '~/migrator.ts';\nimport type { ExpoSQLiteDatabase } from './driver.ts';\n\ninterface MigrationConfig {\n\tjournal: {\n\t\tentries: { idx: number; when: number; tag: string; breakpoints: boolean }[];\n\t};\n\tmigrations: Record<string, string>;\n}\n\nasync function readMigrationFiles({ journal, migrations }: MigrationConfig): Promise<MigrationMeta[]> {\n\tconst migrationQueries: MigrationMeta[] = [];\n\n\tfor await (const journalEntry of journal.entries) {\n\t\tconst query = migrations[`m${journalEntry.idx.toString().padStart(4, '0')}`];\n\n\t\tif (!query) {\n\t\t\tthrow new Error(`Missing migration: ${journalEntry.tag}`);\n\t\t}\n\n\t\ttry {\n\t\t\tconst result = query.split('--> statement-breakpoint').map((it) => {\n\t\t\t\treturn it;\n\t\t\t});\n\n\t\t\tmigrationQueries.push({\n\t\t\t\tsql: result,\n\t\t\t\tbps: journalEntry.breakpoints,\n\t\t\t\tfolderMillis: journalEntry.when,\n\t\t\t\thash: '',\n\t\t\t});\n\t\t} catch {\n\t\t\tthrow new Error(`Failed to parse migration: ${journalEntry.tag}`);\n\t\t}\n\t}\n\n\treturn migrationQueries;\n}\n\nexport async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: ExpoSQLiteDatabase<TSchema>,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = await readMigrationFiles(config);\n\treturn db.dialect.migrate(migrations, db.session);\n}\n\ninterface State {\n\tsuccess: boolean;\n\terror?: Error;\n}\n\ntype Action =\n\t| { type: 'migrating' }\n\t| { type: 'migrated'; payload: true }\n\t| { type: 'error'; payload: Error };\n\nexport const useMigrations = (db: ExpoSQLiteDatabase<any>, migrations: {\n\tjournal: {\n\t\tentries: { idx: number; when: number; tag: string; breakpoints: boolean }[];\n\t};\n\tmigrations: Record<string, string>;\n}): State => {\n\tconst initialState: State = {\n\t\tsuccess: false,\n\t\terror: undefined,\n\t};\n\n\tconst fetchReducer = (state: State, action: Action): State => {\n\t\tswitch (action.type) {\n\t\t\tcase 'migrating': {\n\t\t\t\treturn { ...initialState };\n\t\t\t}\n\t\t\tcase 'migrated': {\n\t\t\t\treturn { ...initialState, success: action.payload };\n\t\t\t}\n\t\t\tcase 'error': {\n\t\t\t\treturn { ...initialState, error: action.payload };\n\t\t\t}\n\t\t\tdefault: {\n\t\t\t\treturn state;\n\t\t\t}\n\t\t}\n\t};\n\n\tconst [state, dispatch] = useReducer(fetchReducer, initialState);\n\n\tuseEffect(() => {\n\t\tdispatch({ type: 'migrating' });\n\t\tmigrate(db, migrations as any).then(() => {\n\t\t\tdispatch({ type: 'migrated', payload: true });\n\t\t}).catch((error) => {\n\t\t\tdispatch({ type: 'error', payload: error as Error });\n\t\t});\n\t}, []);\n\n\treturn state;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAsC;AAWtC,eAAe,mBAAmB,EAAE,SAAS,WAAW,GAA8C;AACrG,QAAM,mBAAoC,CAAC;AAE3C,mBAAiB,gBAAgB,QAAQ,SAAS;AACjD,UAAM,QAAQ,WAAW,IAAI,aAAa,IAAI,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE;AAE3E,QAAI,CAAC,OAAO;AACX,YAAM,IAAI,MAAM,sBAAsB,aAAa,GAAG,EAAE;AAAA,IACzD;AAEA,QAAI;AACH,YAAM,SAAS,MAAM,MAAM,0BAA0B,EAAE,IAAI,CAAC,OAAO;AAClE,eAAO;AAAA,MACR,CAAC;AAED,uBAAiB,KAAK;AAAA,QACrB,KAAK;AAAA,QACL,KAAK,aAAa;AAAA,QAClB,cAAc,aAAa;AAAA,QAC3B,MAAM;AAAA,MACP,CAAC;AAAA,IACF,QAAQ;AACP,YAAM,IAAI,MAAM,8BAA8B,aAAa,GAAG,EAAE;AAAA,IACjE;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,QACrB,IACA,QACC;AACD,QAAM,aAAa,MAAM,mBAAmB,MAAM;AAClD,SAAO,GAAG,QAAQ,QAAQ,YAAY,GAAG,OAAO;AACjD;AAYO,MAAM,gBAAgB,CAAC,IAA6B,eAK9C;AACZ,QAAM,eAAsB;AAAA,IAC3B,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAEA,QAAM,eAAe,CAACA,QAAc,WAA0B;AAC7D,YAAQ,OAAO,MAAM;AAAA,MACpB,KAAK,aAAa;AACjB,eAAO,EAAE,GAAG,aAAa;AAAA,MAC1B;AAAA,MACA,KAAK,YAAY;AAChB,eAAO,EAAE,GAAG,cAAc,SAAS,OAAO,QAAQ;AAAA,MACnD;AAAA,MACA,KAAK,SAAS;AACb,eAAO,EAAE,GAAG,cAAc,OAAO,OAAO,QAAQ;AAAA,MACjD;AAAA,MACA,SAAS;AACR,eAAOA;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAEA,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAW,cAAc,YAAY;AAE/D,8BAAU,MAAM;AACf,aAAS,EAAE,MAAM,YAAY,CAAC;AAC9B,YAAQ,IAAI,UAAiB,EAAE,KAAK,MAAM;AACzC,eAAS,EAAE,MAAM,YAAY,SAAS,KAAK,CAAC;AAAA,IAC7C,CAAC,EAAE,MAAM,CAAC,UAAU;AACnB,eAAS,EAAE,MAAM,SAAS,SAAS,MAAe,CAAC;AAAA,IACpD,CAAC;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACR;", "names": ["state"]}