{"version": 3, "sources": ["../../../src/gel-core/columns/integer.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '../table.ts';\nimport { GelColumn } from './common.ts';\nimport { GelIntColumnBaseBuilder } from './int.common.ts';\n\nexport type GelIntegerBuilderInitial<TName extends string> = GelIntegerBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'GelInteger';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class GelIntegerBuilder<T extends ColumnBuilderBaseConfig<'number', 'GelInteger'>>\n\textends GelIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelIntegerBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'GelInteger');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelInteger<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelInteger<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelInteger<T extends ColumnBaseConfig<'number', 'GelInteger'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelInteger';\n\n\tgetSQLType(): string {\n\t\treturn 'integer';\n\t}\n}\n\nexport function integer(): GelIntegerBuilderInitial<''>;\nexport function integer<TName extends string>(name: TName): GelIntegerBuilderInitial<TName>;\nexport function integer(name?: string) {\n\treturn new GelIntegerBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,iBAAiB;AAC1B,SAAS,+BAA+B;AAWjC,MAAM,0BACJ,wBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,YAAY;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBAAuE,UAAa;AAAA,EAChG,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,QAAQ,MAAe;AACtC,SAAO,IAAI,kBAAkB,QAAQ,EAAE;AACxC;", "names": []}