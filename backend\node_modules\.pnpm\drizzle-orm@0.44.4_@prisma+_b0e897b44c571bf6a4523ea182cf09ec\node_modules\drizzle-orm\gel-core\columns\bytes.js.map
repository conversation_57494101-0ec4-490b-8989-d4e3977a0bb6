{"version": 3, "sources": ["../../../src/gel-core/columns/bytes.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelBytesBuilderInitial<TName extends string> = GelBytesBuilder<{\n\tname: TName;\n\tdataType: 'buffer';\n\tcolumnType: 'GelBytes';\n\tdata: Uint8Array;\n\tdriverParam: Uint8Array | Buffer;\n\tenumValues: undefined;\n}>;\n\nexport class GelBytesBuilder<T extends ColumnBuilderBaseConfig<'buffer', 'GelBytes'>> extends GelColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'GelBytesBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'buffer', 'GelBytes');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelBytes<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelBytes<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelBytes<T extends ColumnBaseConfig<'buffer', 'GelBytes'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelBytes';\n\n\tgetSQLType(): string {\n\t\treturn 'bytea';\n\t}\n}\n\nexport function bytes(): GelBytesBuilderInitial<''>;\nexport function bytes<TName extends string>(name: TName): GelBytesBuilderInitial<TName>;\nexport function bytes(name?: string) {\n\treturn new GelBytesBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,WAAW,wBAAwB;AAWrC,MAAM,wBAAiF,iBAAoB;AAAA,EACjH,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,UAAU;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC4C;AAC5C,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,iBAAmE,UAAa;AAAA,EAC5F,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,MAAM,MAAe;AACpC,SAAO,IAAI,gBAAgB,QAAQ,EAAE;AACtC;", "names": []}