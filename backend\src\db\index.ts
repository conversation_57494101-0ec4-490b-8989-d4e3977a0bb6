import { drizzle } from 'drizzle-orm/d1';
import * as schema from './schema';

// Cloudflare Workers环境接口
export interface Env {
  DB: D1Database;
  // 其他环境变量可以在这里添加
  JWT_SECRET?: string;
  ENCRYPTION_KEY?: string;
}

// 创建数据库连接
export function createDb(database: D1Database) {
  return drizzle(database, { schema });
}

// 数据库类型
export type Database = ReturnType<typeof createDb>;

// 导出schema以便在其他地方使用
export * from './schema';
