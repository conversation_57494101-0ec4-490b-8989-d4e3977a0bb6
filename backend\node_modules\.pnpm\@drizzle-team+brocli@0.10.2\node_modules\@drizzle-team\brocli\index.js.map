{"version": 3, "sources": ["../node_modules/.pnpm/clone@2.1.2/node_modules/clone/clone.js", "../node_modules/.pnpm/shell-quote@1.8.1/node_modules/shell-quote/quote.js", "../node_modules/.pnpm/shell-quote@1.8.1/node_modules/shell-quote/parse.js", "../node_modules/.pnpm/shell-quote@1.8.1/node_modules/shell-quote/index.js", "../src/brocli-error.ts", "../src/command-core.ts", "../src/event-handler.ts", "../src/util.ts", "../src/option-builder.ts"], "sourcesContent": ["var clone = (function() {\n'use strict';\n\nfunction _instanceof(obj, type) {\n  return type != null && obj instanceof type;\n}\n\nvar nativeMap;\ntry {\n  nativeMap = Map;\n} catch(_) {\n  // maybe a reference error because no `Map`. Give it a dummy value that no\n  // value will ever be an instanceof.\n  nativeMap = function() {};\n}\n\nvar nativeSet;\ntry {\n  nativeSet = Set;\n} catch(_) {\n  nativeSet = function() {};\n}\n\nvar nativePromise;\ntry {\n  nativePromise = Promise;\n} catch(_) {\n  nativePromise = function() {};\n}\n\n/**\n * Clones (copies) an Object using deep copying.\n *\n * This function supports circular references by default, but if you are certain\n * there are no circular references in your object, you can save some CPU time\n * by calling clone(obj, false).\n *\n * Caution: if `circular` is false and `parent` contains circular references,\n * your program may enter an infinite loop and crash.\n *\n * @param `parent` - the object to be cloned\n * @param `circular` - set to true if the object to be cloned may contain\n *    circular references. (optional - true by default)\n * @param `depth` - set to a number if the object is only to be cloned to\n *    a particular depth. (optional - defaults to Infinity)\n * @param `prototype` - sets the prototype to be used when cloning an object.\n *    (optional - defaults to parent prototype).\n * @param `includeNonEnumerable` - set to true if the non-enumerable properties\n *    should be cloned as well. Non-enumerable properties on the prototype\n *    chain will be ignored. (optional - false by default)\n*/\nfunction clone(parent, circular, depth, prototype, includeNonEnumerable) {\n  if (typeof circular === 'object') {\n    depth = circular.depth;\n    prototype = circular.prototype;\n    includeNonEnumerable = circular.includeNonEnumerable;\n    circular = circular.circular;\n  }\n  // maintain two arrays for circular references, where corresponding parents\n  // and children have the same index\n  var allParents = [];\n  var allChildren = [];\n\n  var useBuffer = typeof Buffer != 'undefined';\n\n  if (typeof circular == 'undefined')\n    circular = true;\n\n  if (typeof depth == 'undefined')\n    depth = Infinity;\n\n  // recurse this function so we don't reset allParents and allChildren\n  function _clone(parent, depth) {\n    // cloning null always returns null\n    if (parent === null)\n      return null;\n\n    if (depth === 0)\n      return parent;\n\n    var child;\n    var proto;\n    if (typeof parent != 'object') {\n      return parent;\n    }\n\n    if (_instanceof(parent, nativeMap)) {\n      child = new nativeMap();\n    } else if (_instanceof(parent, nativeSet)) {\n      child = new nativeSet();\n    } else if (_instanceof(parent, nativePromise)) {\n      child = new nativePromise(function (resolve, reject) {\n        parent.then(function(value) {\n          resolve(_clone(value, depth - 1));\n        }, function(err) {\n          reject(_clone(err, depth - 1));\n        });\n      });\n    } else if (clone.__isArray(parent)) {\n      child = [];\n    } else if (clone.__isRegExp(parent)) {\n      child = new RegExp(parent.source, __getRegExpFlags(parent));\n      if (parent.lastIndex) child.lastIndex = parent.lastIndex;\n    } else if (clone.__isDate(parent)) {\n      child = new Date(parent.getTime());\n    } else if (useBuffer && Buffer.isBuffer(parent)) {\n      if (Buffer.allocUnsafe) {\n        // Node.js >= 4.5.0\n        child = Buffer.allocUnsafe(parent.length);\n      } else {\n        // Older Node.js versions\n        child = new Buffer(parent.length);\n      }\n      parent.copy(child);\n      return child;\n    } else if (_instanceof(parent, Error)) {\n      child = Object.create(parent);\n    } else {\n      if (typeof prototype == 'undefined') {\n        proto = Object.getPrototypeOf(parent);\n        child = Object.create(proto);\n      }\n      else {\n        child = Object.create(prototype);\n        proto = prototype;\n      }\n    }\n\n    if (circular) {\n      var index = allParents.indexOf(parent);\n\n      if (index != -1) {\n        return allChildren[index];\n      }\n      allParents.push(parent);\n      allChildren.push(child);\n    }\n\n    if (_instanceof(parent, nativeMap)) {\n      parent.forEach(function(value, key) {\n        var keyChild = _clone(key, depth - 1);\n        var valueChild = _clone(value, depth - 1);\n        child.set(keyChild, valueChild);\n      });\n    }\n    if (_instanceof(parent, nativeSet)) {\n      parent.forEach(function(value) {\n        var entryChild = _clone(value, depth - 1);\n        child.add(entryChild);\n      });\n    }\n\n    for (var i in parent) {\n      var attrs;\n      if (proto) {\n        attrs = Object.getOwnPropertyDescriptor(proto, i);\n      }\n\n      if (attrs && attrs.set == null) {\n        continue;\n      }\n      child[i] = _clone(parent[i], depth - 1);\n    }\n\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(parent);\n      for (var i = 0; i < symbols.length; i++) {\n        // Don't need to worry about cloning a symbol because it is a primitive,\n        // like a number or string.\n        var symbol = symbols[i];\n        var descriptor = Object.getOwnPropertyDescriptor(parent, symbol);\n        if (descriptor && !descriptor.enumerable && !includeNonEnumerable) {\n          continue;\n        }\n        child[symbol] = _clone(parent[symbol], depth - 1);\n        if (!descriptor.enumerable) {\n          Object.defineProperty(child, symbol, {\n            enumerable: false\n          });\n        }\n      }\n    }\n\n    if (includeNonEnumerable) {\n      var allPropertyNames = Object.getOwnPropertyNames(parent);\n      for (var i = 0; i < allPropertyNames.length; i++) {\n        var propertyName = allPropertyNames[i];\n        var descriptor = Object.getOwnPropertyDescriptor(parent, propertyName);\n        if (descriptor && descriptor.enumerable) {\n          continue;\n        }\n        child[propertyName] = _clone(parent[propertyName], depth - 1);\n        Object.defineProperty(child, propertyName, {\n          enumerable: false\n        });\n      }\n    }\n\n    return child;\n  }\n\n  return _clone(parent, depth);\n}\n\n/**\n * Simple flat clone using prototype, accepts only objects, usefull for property\n * override on FLAT configuration object (no nested props).\n *\n * USE WITH CAUTION! This may not behave as you wish if you do not know how this\n * works.\n */\nclone.clonePrototype = function clonePrototype(parent) {\n  if (parent === null)\n    return null;\n\n  var c = function () {};\n  c.prototype = parent;\n  return new c();\n};\n\n// private utility functions\n\nfunction __objToStr(o) {\n  return Object.prototype.toString.call(o);\n}\nclone.__objToStr = __objToStr;\n\nfunction __isDate(o) {\n  return typeof o === 'object' && __objToStr(o) === '[object Date]';\n}\nclone.__isDate = __isDate;\n\nfunction __isArray(o) {\n  return typeof o === 'object' && __objToStr(o) === '[object Array]';\n}\nclone.__isArray = __isArray;\n\nfunction __isRegExp(o) {\n  return typeof o === 'object' && __objToStr(o) === '[object RegExp]';\n}\nclone.__isRegExp = __isRegExp;\n\nfunction __getRegExpFlags(re) {\n  var flags = '';\n  if (re.global) flags += 'g';\n  if (re.ignoreCase) flags += 'i';\n  if (re.multiline) flags += 'm';\n  return flags;\n}\nclone.__getRegExpFlags = __getRegExpFlags;\n\nreturn clone;\n})();\n\nif (typeof module === 'object' && module.exports) {\n  module.exports = clone;\n}\n", "'use strict';\n\nmodule.exports = function quote(xs) {\n\treturn xs.map(function (s) {\n\t\tif (s && typeof s === 'object') {\n\t\t\treturn s.op.replace(/(.)/g, '\\\\$1');\n\t\t}\n\t\tif ((/[\"\\s]/).test(s) && !(/'/).test(s)) {\n\t\t\treturn \"'\" + s.replace(/(['\\\\])/g, '\\\\$1') + \"'\";\n\t\t}\n\t\tif ((/[\"'\\s]/).test(s)) {\n\t\t\treturn '\"' + s.replace(/([\"\\\\$`!])/g, '\\\\$1') + '\"';\n\t\t}\n\t\treturn String(s).replace(/([A-Za-z]:)?([#!\"$&'()*,:;<=>?@[\\\\\\]^`{|}])/g, '$1\\\\$2');\n\t}).join(' ');\n};\n", "'use strict';\n\n// '<(' is process substitution operator and\n// can be parsed the same as control operator\nvar CONTROL = '(?:' + [\n\t'\\\\|\\\\|',\n\t'\\\\&\\\\&',\n\t';;',\n\t'\\\\|\\\\&',\n\t'\\\\<\\\\(',\n\t'\\\\<\\\\<\\\\<',\n\t'>>',\n\t'>\\\\&',\n\t'<\\\\&',\n\t'[&;()|<>]'\n].join('|') + ')';\nvar controlRE = new RegExp('^' + CONTROL + '$');\nvar META = '|&;()<> \\\\t';\nvar SINGLE_QUOTE = '\"((\\\\\\\\\"|[^\"])*?)\"';\nvar DOUBLE_QUOTE = '\\'((\\\\\\\\\\'|[^\\'])*?)\\'';\nvar hash = /^#$/;\n\nvar SQ = \"'\";\nvar DQ = '\"';\nvar DS = '$';\n\nvar TOKEN = '';\nvar mult = 0x100000000; // Math.pow(16, 8);\nfor (var i = 0; i < 4; i++) {\n\tTOKEN += (mult * Math.random()).toString(16);\n}\nvar startsWithToken = new RegExp('^' + TOKEN);\n\nfunction matchAll(s, r) {\n\tvar origIndex = r.lastIndex;\n\n\tvar matches = [];\n\tvar matchObj;\n\n\twhile ((matchObj = r.exec(s))) {\n\t\tmatches.push(matchObj);\n\t\tif (r.lastIndex === matchObj.index) {\n\t\t\tr.lastIndex += 1;\n\t\t}\n\t}\n\n\tr.lastIndex = origIndex;\n\n\treturn matches;\n}\n\nfunction getVar(env, pre, key) {\n\tvar r = typeof env === 'function' ? env(key) : env[key];\n\tif (typeof r === 'undefined' && key != '') {\n\t\tr = '';\n\t} else if (typeof r === 'undefined') {\n\t\tr = '$';\n\t}\n\n\tif (typeof r === 'object') {\n\t\treturn pre + TOKEN + JSON.stringify(r) + TOKEN;\n\t}\n\treturn pre + r;\n}\n\nfunction parseInternal(string, env, opts) {\n\tif (!opts) {\n\t\topts = {};\n\t}\n\tvar BS = opts.escape || '\\\\';\n\tvar BAREWORD = '(\\\\' + BS + '[\\'\"' + META + ']|[^\\\\s\\'\"' + META + '])+';\n\n\tvar chunker = new RegExp([\n\t\t'(' + CONTROL + ')', // control chars\n\t\t'(' + BAREWORD + '|' + SINGLE_QUOTE + '|' + DOUBLE_QUOTE + ')+'\n\t].join('|'), 'g');\n\n\tvar matches = matchAll(string, chunker);\n\n\tif (matches.length === 0) {\n\t\treturn [];\n\t}\n\tif (!env) {\n\t\tenv = {};\n\t}\n\n\tvar commented = false;\n\n\treturn matches.map(function (match) {\n\t\tvar s = match[0];\n\t\tif (!s || commented) {\n\t\t\treturn void undefined;\n\t\t}\n\t\tif (controlRE.test(s)) {\n\t\t\treturn { op: s };\n\t\t}\n\n\t\t// Hand-written scanner/parser for Bash quoting rules:\n\t\t//\n\t\t// 1. inside single quotes, all characters are printed literally.\n\t\t// 2. inside double quotes, all characters are printed literally\n\t\t//    except variables prefixed by '$' and backslashes followed by\n\t\t//    either a double quote or another backslash.\n\t\t// 3. outside of any quotes, backslashes are treated as escape\n\t\t//    characters and not printed (unless they are themselves escaped)\n\t\t// 4. quote context can switch mid-token if there is no whitespace\n\t\t//     between the two quote contexts (e.g. all'one'\"token\" parses as\n\t\t//     \"allonetoken\")\n\t\tvar quote = false;\n\t\tvar esc = false;\n\t\tvar out = '';\n\t\tvar isGlob = false;\n\t\tvar i;\n\n\t\tfunction parseEnvVar() {\n\t\t\ti += 1;\n\t\t\tvar varend;\n\t\t\tvar varname;\n\t\t\tvar char = s.charAt(i);\n\n\t\t\tif (char === '{') {\n\t\t\t\ti += 1;\n\t\t\t\tif (s.charAt(i) === '}') {\n\t\t\t\t\tthrow new Error('Bad substitution: ' + s.slice(i - 2, i + 1));\n\t\t\t\t}\n\t\t\t\tvarend = s.indexOf('}', i);\n\t\t\t\tif (varend < 0) {\n\t\t\t\t\tthrow new Error('Bad substitution: ' + s.slice(i));\n\t\t\t\t}\n\t\t\t\tvarname = s.slice(i, varend);\n\t\t\t\ti = varend;\n\t\t\t} else if ((/[*@#?$!_-]/).test(char)) {\n\t\t\t\tvarname = char;\n\t\t\t\ti += 1;\n\t\t\t} else {\n\t\t\t\tvar slicedFromI = s.slice(i);\n\t\t\t\tvarend = slicedFromI.match(/[^\\w\\d_]/);\n\t\t\t\tif (!varend) {\n\t\t\t\t\tvarname = slicedFromI;\n\t\t\t\t\ti = s.length;\n\t\t\t\t} else {\n\t\t\t\t\tvarname = slicedFromI.slice(0, varend.index);\n\t\t\t\t\ti += varend.index - 1;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn getVar(env, '', varname);\n\t\t}\n\n\t\tfor (i = 0; i < s.length; i++) {\n\t\t\tvar c = s.charAt(i);\n\t\t\tisGlob = isGlob || (!quote && (c === '*' || c === '?'));\n\t\t\tif (esc) {\n\t\t\t\tout += c;\n\t\t\t\tesc = false;\n\t\t\t} else if (quote) {\n\t\t\t\tif (c === quote) {\n\t\t\t\t\tquote = false;\n\t\t\t\t} else if (quote == SQ) {\n\t\t\t\t\tout += c;\n\t\t\t\t} else { // Double quote\n\t\t\t\t\tif (c === BS) {\n\t\t\t\t\t\ti += 1;\n\t\t\t\t\t\tc = s.charAt(i);\n\t\t\t\t\t\tif (c === DQ || c === BS || c === DS) {\n\t\t\t\t\t\t\tout += c;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tout += BS + c;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (c === DS) {\n\t\t\t\t\t\tout += parseEnvVar();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tout += c;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (c === DQ || c === SQ) {\n\t\t\t\tquote = c;\n\t\t\t} else if (controlRE.test(c)) {\n\t\t\t\treturn { op: s };\n\t\t\t} else if (hash.test(c)) {\n\t\t\t\tcommented = true;\n\t\t\t\tvar commentObj = { comment: string.slice(match.index + i + 1) };\n\t\t\t\tif (out.length) {\n\t\t\t\t\treturn [out, commentObj];\n\t\t\t\t}\n\t\t\t\treturn [commentObj];\n\t\t\t} else if (c === BS) {\n\t\t\t\tesc = true;\n\t\t\t} else if (c === DS) {\n\t\t\t\tout += parseEnvVar();\n\t\t\t} else {\n\t\t\t\tout += c;\n\t\t\t}\n\t\t}\n\n\t\tif (isGlob) {\n\t\t\treturn { op: 'glob', pattern: out };\n\t\t}\n\n\t\treturn out;\n\t}).reduce(function (prev, arg) { // finalize parsed arguments\n\t\t// TODO: replace this whole reduce with a concat\n\t\treturn typeof arg === 'undefined' ? prev : prev.concat(arg);\n\t}, []);\n}\n\nmodule.exports = function parse(s, env, opts) {\n\tvar mapped = parseInternal(s, env, opts);\n\tif (typeof env !== 'function') {\n\t\treturn mapped;\n\t}\n\treturn mapped.reduce(function (acc, s) {\n\t\tif (typeof s === 'object') {\n\t\t\treturn acc.concat(s);\n\t\t}\n\t\tvar xs = s.split(RegExp('(' + TOKEN + '.*?' + TOKEN + ')', 'g'));\n\t\tif (xs.length === 1) {\n\t\t\treturn acc.concat(xs[0]);\n\t\t}\n\t\treturn acc.concat(xs.filter(Boolean).map(function (x) {\n\t\t\tif (startsWithToken.test(x)) {\n\t\t\t\treturn JSON.parse(x.split(TOKEN)[1]);\n\t\t\t}\n\t\t\treturn x;\n\t\t}));\n\t}, []);\n};\n", "'use strict';\n\nexports.quote = require('./quote');\nexports.parse = require('./parse');\n", "import type { BroCliEvent } from './event-handler';\n\n/**\n * Internal error class used to bypass runCli's logging without stack trace\n *\n * Used only for malformed commands and options\n */\nexport class BroCliError extends Error {\n\tconstructor(message: string | undefined, public event?: BroCliEvent) {\n\t\tconst errPrefix = 'Bro<PERSON>li error: ';\n\t\tsuper(message === undefined ? message : `${errPrefix}${message}`);\n\t}\n}\n", "import clone from 'clone';\nimport { BroCliError } from './brocli-error';\nimport { defaultEvent<PERSON>and<PERSON>, type EventHandler, eventHandlerWrapper } from './event-handler';\nimport {\n\ttype GenericBuilderInternals,\n\ttype GenericBuilderInternalsFields,\n\ttype OutputType,\n\ttype ProcessedBuilderConfig,\n\ttype ProcessedOptions,\n\ttype TypeOf,\n} from './option-builder';\nimport { executeOrLog, isInt, shellArgs } from './util';\n\n// Type area\nexport type CommandHandler<\n\tTOpts extends Record<string, GenericBuilderInternals> | undefined =\n\t\t| Record<string, GenericBuilderInternals>\n\t\t| undefined,\n> = (\n\toptions: TOpts extends Record<string, GenericBuilderInternals> ? TypeOf<TOpts> : undefined,\n) => any;\n\nexport type CommandInfo = {\n\tname: string;\n\taliases?: [string, ...string[]];\n\tdesc?: string;\n\tshortDesc?: string;\n\thidden?: boolean;\n\toptions?: Record<string, ProcessedBuilderConfig>;\n\tmetadata?: any;\n\tsubcommands?: CommandsInfo;\n};\n\nexport type CommandsInfo = Record<string, CommandInfo>;\n\nexport type EventType = 'before' | 'after';\n\nexport type BroCliConfig = {\n\tname?: string;\n\tdescription?: string;\n\targSource?: string[];\n\thelp?: string | Function;\n\tversion?: string | Function;\n\tomitKeysOfUndefinedOptions?: boolean;\n\thook?: (event: EventType, command: Command) => any;\n\ttheme?: EventHandler;\n};\n\nexport type GenericCommandHandler = (options?: Record<string, OutputType> | undefined) => any;\n\nexport type RawCommand<\n\tTOpts extends Record<string, GenericBuilderInternals> | undefined =\n\t\t| Record<string, GenericBuilderInternals>\n\t\t| undefined,\n\tTOptsData = TOpts extends Record<string, GenericBuilderInternals> ? TypeOf<TOpts> : undefined,\n\tTTransformed = TOptsData extends undefined ? undefined : TOptsData,\n> = {\n\tname?: string;\n\taliases?: [string, ...string[]];\n\tdesc?: string;\n\tshortDesc?: string;\n\thidden?: boolean;\n\toptions?: TOpts;\n\thelp?: string | Function;\n\ttransform?: (options: TOptsData) => TTransformed;\n\thandler?: (options: Awaited<TTransformed>) => any;\n\tsubcommands?: [Command, ...Command[]];\n\tmetadata?: any;\n};\n\nexport type AnyRawCommand<\n\tTOpts extends Record<string, GenericBuilderInternals> | undefined =\n\t\t| Record<string, GenericBuilderInternals>\n\t\t| undefined,\n> = {\n\tname?: string;\n\taliases?: [string, ...string[]];\n\tdesc?: string;\n\tshortDesc?: string;\n\thidden?: boolean;\n\toptions?: TOpts;\n\thelp?: string | Function;\n\ttransform?: GenericCommandHandler;\n\thandler?: GenericCommandHandler;\n\tsubcommands?: [Command, ...Command[]];\n\tmetadata?: any;\n};\n\nexport type Command<TOptsType = any, TTransformedType = any> = {\n\tname: string;\n\taliases?: [string, ...string[]];\n\tdesc?: string;\n\tshortDesc?: string;\n\thidden?: boolean;\n\toptions?: ProcessedOptions;\n\thelp?: string | Function;\n\ttransform?: GenericCommandHandler;\n\thandler?: GenericCommandHandler;\n\tsubcommands?: [Command, ...Command[]];\n\tparent?: Command;\n\tmetadata?: any;\n};\n\nexport type CommandCandidate = {\n\tdata: string;\n\toriginalIndex: number;\n};\n\nexport type InnerCommandParseRes = {\n\tcommand: Command | undefined;\n\targs: string[];\n};\n\nexport type TestResult<THandlerInput> = {\n\ttype: 'handler';\n\toptions: THandlerInput;\n} | {\n\ttype: 'help' | 'version';\n} | {\n\ttype: 'error';\n\terror: unknown;\n};\n\nconst generatePrefix = (name: string) => name.startsWith('-') ? name : name.length > 1 ? `--${name}` : `-${name}`;\n\nconst validateOptions = <TOptionConfig extends Record<string, GenericBuilderInternals>>(\n\tconfig: TOptionConfig,\n): ProcessedOptions<TOptionConfig> => {\n\tconst cloned = clone(config);\n\n\tconst entries: [string, GenericBuilderInternalsFields][] = [];\n\n\tconst storedNames: [string, ...string[]][] = [];\n\n\tconst cfgEntries = Object.entries(cloned);\n\n\tfor (const [key, value] of cfgEntries) {\n\t\tconst cfg = value._.config;\n\n\t\tif (cfg.name === undefined) cfg.name = key;\n\n\t\tif (cfg.type === 'positional') continue;\n\n\t\tif (cfg.name!.includes('=')) {\n\t\t\tthrow new BroCliError(\n\t\t\t\t`Can't define option '${generatePrefix(cfg.name)}' - option names and aliases cannot contain '='!`,\n\t\t\t);\n\t\t}\n\n\t\tfor (const alias of cfg.aliases) {\n\t\t\tif (alias.includes('=')) {\n\t\t\t\tthrow new BroCliError(\n\t\t\t\t\t`Can't define option '${generatePrefix(cfg.name)}' - option names and aliases cannot contain '='!`,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tcfg.name = generatePrefix(cfg.name);\n\n\t\tcfg.aliases = cfg.aliases.map((a) => generatePrefix(a));\n\t}\n\n\tfor (const [key, value] of cfgEntries) {\n\t\tconst cfg = value._.config;\n\n\t\tif (cfg.type === 'positional') {\n\t\t\tentries.push([key, { config: cfg, $output: undefined as any }]);\n\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst reservedNames = ['--help', '-h', '--version', '-v'];\n\n\t\tconst allNames = [cfg.name, ...cfg.aliases];\n\n\t\tfor (const name of allNames) {\n\t\t\tconst match = reservedNames.find((n) => n === name);\n\t\t\tif (match) throw new BroCliError(`Can't define option '${cfg.name}' - name '${match}' is reserved!`);\n\t\t}\n\n\t\tfor (const storage of storedNames) {\n\t\t\tconst nameOccupier = storage.find((e) => e === cfg.name);\n\n\t\t\tif (!nameOccupier) continue;\n\n\t\t\tthrow new BroCliError(\n\t\t\t\t`Can't define option '${cfg.name}' - name is already in use by option '${storage[0]}'!`,\n\t\t\t);\n\t\t}\n\n\t\tfor (const alias of cfg.aliases) {\n\t\t\tfor (const storage of storedNames) {\n\t\t\t\tconst nameOccupier = storage.find((e) => e === alias);\n\n\t\t\t\tif (!nameOccupier) continue;\n\n\t\t\t\tthrow new BroCliError(\n\t\t\t\t\t`Can't define option '${cfg.name}' - alias '${alias}' is already in use by option '${storage[0]}'!`,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst currentNames = [cfg.name!, ...cfg.aliases] as [string, ...string[]];\n\n\t\tstoredNames.push(currentNames);\n\n\t\tcurrentNames.forEach((name, idx) => {\n\t\t\tif (currentNames.findIndex((e) => e === name) === idx) return;\n\n\t\t\tthrow new BroCliError(\n\t\t\t\t`Can't define option '${cfg.name}' - duplicate alias '${name}'!`,\n\t\t\t);\n\t\t});\n\n\t\tentries.push([key, { config: cfg, $output: undefined as any }]);\n\t}\n\n\treturn Object.fromEntries(entries) as ProcessedOptions<any>;\n};\n\nconst assignParent = (parent: Command, subcommands: Command[]) =>\n\tsubcommands.forEach((e) => {\n\t\te.parent = parent;\n\t\tif (e.subcommands) assignParent(e, e.subcommands);\n\t});\n\nexport const command = <\n\tTOpts extends Record<string, GenericBuilderInternals> | undefined,\n\tTOptsData = TOpts extends Record<string, GenericBuilderInternals> ? TypeOf<TOpts> : undefined,\n\tTTransformed = TOptsData,\n>(command: RawCommand<TOpts, TOptsData, TTransformed>): Command<TOptsData, Awaited<TTransformed>> => {\n\tconst allNames = command.aliases ? [command.name, ...command.aliases] : [command.name];\n\n\tconst cmd: Command = clone(command) as any;\n\tif (\n\t\t(<AnyRawCommand> command).subcommands && command.options\n\t\t&& Object.values(command.options).find((opt) => opt._.config.type === 'positional')\n\t) {\n\t\tthrow new BroCliError(\n\t\t\t`Can't define command '${cmd.name}' - command can't have subcommands and positional args at the same time!`,\n\t\t);\n\t}\n\n\tif (!command.handler && !command.subcommands) {\n\t\tthrow new BroCliError(\n\t\t\t`Can't define command '${cmd.name}' - command without subcommands must have a handler present!`,\n\t\t);\n\t}\n\n\tconst processedOptions = command.options ? validateOptions(command.options) : undefined;\n\tcmd.options = processedOptions;\n\n\tcmd.name = cmd.name ?? cmd.aliases?.shift();\n\n\tif (!cmd.name) throw new BroCliError(`Can't define command without name!`);\n\n\tcmd.aliases = cmd.aliases?.length ? cmd.aliases : undefined;\n\n\tif (cmd.name.startsWith('-')) {\n\t\tthrow new BroCliError(`Can't define command '${cmd.name}' - command name can't start with '-'!`);\n\t}\n\n\tcmd.aliases?.forEach((a) => {\n\t\tif (a.startsWith('-')) {\n\t\t\tthrow new BroCliError(`Can't define command '${cmd.name}' - command aliases can't start with '-'!`);\n\t\t}\n\t});\n\n\tallNames.forEach((n, i) => {\n\t\tif (n === 'help') {\n\t\t\tthrow new BroCliError(\n\t\t\t\t`Can't define command '${cmd.name}' - 'help' is a reserved name. If you want to redefine help message - do so in runCli's config.`,\n\t\t\t);\n\t\t}\n\n\t\tconst lCaseName = n?.toLowerCase();\n\t\tif (lCaseName === '0' || lCaseName === '1' || lCaseName === 'true' || lCaseName === 'false') {\n\t\t\tthrow new BroCliError(\n\t\t\t\t`Can't define command '${cmd.name}' - '${n}' is a reserved for boolean values name!`,\n\t\t\t);\n\t\t}\n\n\t\tconst idx = allNames.findIndex((an) => an === n);\n\n\t\tif (idx !== i) throw new BroCliError(`Can't define command '${cmd.name}' - duplicate alias '${n}'!`);\n\t});\n\n\tif (cmd.subcommands) {\n\t\tassignParent(cmd, cmd.subcommands);\n\t}\n\n\treturn cmd;\n};\n\nconst getCommandInner = (\n\tcommands: Command[],\n\tcandidates: CommandCandidate[],\n\targs: string[],\n\tcliName: string | undefined,\n\tcliDescription: string | undefined,\n): InnerCommandParseRes => {\n\tconst { data: arg, originalIndex: index } = candidates.shift()!;\n\n\tconst command = commands.find((c) => {\n\t\tconst names = c.aliases ? [c.name, ...c.aliases] : [c.name];\n\t\tconst res = names.find((name) => name === arg);\n\n\t\treturn res;\n\t});\n\n\tif (!command) {\n\t\treturn {\n\t\t\tcommand,\n\t\t\targs,\n\t\t};\n\t}\n\n\tconst newArgs = removeByIndex(args, index);\n\n\tif (!candidates.length || !command.subcommands) {\n\t\treturn {\n\t\t\tcommand,\n\t\t\targs: newArgs,\n\t\t};\n\t}\n\n\tconst newCandidates = candidates.map((c) => ({ data: c.data, originalIndex: c.originalIndex - 1 }));\n\n\tconst subcommand = getCommandInner(command.subcommands!, newCandidates, newArgs, cliName, cliDescription);\n\n\tif (!subcommand.command) {\n\t\tthrow new BroCliError(undefined, {\n\t\t\ttype: 'error',\n\t\t\tviolation: 'unknown_subcommand_error',\n\t\t\tname: cliName,\n\t\t\tdescription: cliDescription,\n\t\t\tcommand,\n\t\t\toffender: candidates[0]!.data,\n\t\t});\n\t}\n\n\treturn subcommand;\n};\n\nconst getCommand = (\n\tcommands: Command[],\n\targs: string[],\n\tcliName: string | undefined,\n\tcliDescription: string | undefined,\n) => {\n\tconst candidates: CommandCandidate[] = [];\n\n\tfor (let i = 0; i < args.length; ++i) {\n\t\tconst arg = args[i]!;\n\t\tif (arg === '--help' || arg === '-h' || arg === '--version' || arg === '-v') {\n\t\t\tconst lCaseNext = args[i + 1]?.toLowerCase();\n\t\t\tif (lCaseNext === '0' || lCaseNext === '1' || lCaseNext === 'true' || lCaseNext === 'false') ++i;\n\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (arg?.startsWith('-')) {\n\t\t\tif (!arg.includes('=')) ++i;\n\n\t\t\tcontinue;\n\t\t}\n\n\t\tcandidates.push({\n\t\t\tdata: arg,\n\t\t\toriginalIndex: i,\n\t\t});\n\t}\n\n\tif (!candidates.length) {\n\t\treturn {\n\t\t\tcommand: undefined,\n\t\t\targs,\n\t\t};\n\t}\n\n\tconst firstCandidate = candidates[0]!;\n\n\tif (firstCandidate.data === 'help') {\n\t\treturn {\n\t\t\tcommand: 'help' as const,\n\t\t\targs: removeByIndex(args, firstCandidate.originalIndex),\n\t\t};\n\t}\n\n\tconst { command, args: argsRes } = getCommandInner(commands, candidates, args, cliName, cliDescription);\n\n\tif (!command) {\n\t\tthrow new BroCliError(undefined, {\n\t\t\ttype: 'error',\n\t\t\tviolation: 'unknown_command_error',\n\t\t\tcommands,\n\t\t\tname: cliName,\n\t\t\tdescription: cliDescription,\n\t\t\toffender: firstCandidate.data,\n\t\t});\n\t}\n\n\treturn {\n\t\tcommand,\n\t\targs: argsRes,\n\t};\n};\n\nconst parseArg = (\n\tcommand: Command,\n\toptions: [string, ProcessedBuilderConfig][],\n\tpositionals: [string, ProcessedBuilderConfig][],\n\targ: string,\n\tnextArg: string | undefined,\n\tcliName: string | undefined,\n\tcliDescription: string | undefined,\n) => {\n\tlet data: OutputType = undefined;\n\n\tconst argSplit = arg.split('=');\n\tconst hasEq = arg.includes('=');\n\n\tconst namePart = argSplit.shift()!;\n\tconst dataPart = hasEq ? argSplit.join('=') : nextArg;\n\tlet skipNext = !hasEq;\n\n\tif (namePart === '--help' || namePart === '-h') {\n\t\treturn {\n\t\t\tisHelp: true,\n\t\t};\n\t}\n\n\tif (namePart === '--version' || namePart === '-v') {\n\t\treturn {\n\t\t\tisVersion: true,\n\t\t};\n\t}\n\n\tif (!arg.startsWith('-')) {\n\t\tif (!positionals.length) return {};\n\n\t\tconst pos = positionals.shift()!;\n\n\t\tif (pos[1].enumVals && !pos[1].enumVals.find((val) => val === arg)) {\n\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\ttype: 'error',\n\t\t\t\tname: cliName,\n\t\t\t\tdescription: cliDescription,\n\t\t\t\tviolation: 'enum_violation',\n\t\t\t\tcommand,\n\t\t\t\toption: pos[1],\n\t\t\t\toffender: {\n\t\t\t\t\tdataPart: arg,\n\t\t\t\t},\n\t\t\t});\n\t\t}\n\n\t\tdata = arg;\n\n\t\treturn {\n\t\t\tdata,\n\t\t\tskipNext: false,\n\t\t\tname: pos[0],\n\t\t\toption: pos[1],\n\t\t};\n\t}\n\n\tconst option = options.find(([optKey, opt]) => {\n\t\tconst names = [opt.name!, ...opt.aliases];\n\n\t\tif (opt.type === 'boolean') {\n\t\t\tconst match = names.find((name) => name === namePart);\n\t\t\tif (!match) return false;\n\n\t\t\tlet lcaseData = dataPart?.toLowerCase();\n\n\t\t\tif (!hasEq && nextArg?.startsWith('-')) {\n\t\t\t\tdata = true;\n\t\t\t\tskipNext = false;\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (lcaseData === undefined || lcaseData === '' || lcaseData === 'true' || lcaseData === '1') {\n\t\t\t\tdata = true;\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (lcaseData === 'false' || lcaseData === '0') {\n\t\t\t\tdata = false;\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (!hasEq) {\n\t\t\t\tdata = true;\n\t\t\t\tskipNext = false;\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\ttype: 'error',\n\t\t\t\tname: cliName,\n\t\t\t\tdescription: cliDescription,\n\t\t\t\tviolation: 'invalid_boolean_syntax',\n\t\t\t\toption: opt,\n\t\t\t\tcommand,\n\t\t\t\toffender: {\n\t\t\t\t\tnamePart,\n\t\t\t\t\tdataPart,\n\t\t\t\t},\n\t\t\t});\n\t\t} else {\n\t\t\tconst match = names.find((name) => name === namePart);\n\n\t\t\tif (!match) return false;\n\n\t\t\tif (opt.type === 'string') {\n\t\t\t\tif (!hasEq && nextArg === undefined) {\n\t\t\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\t\t\ttype: 'error',\n\t\t\t\t\t\tname: cliName,\n\t\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\t\tviolation: 'invalid_string_syntax',\n\t\t\t\t\t\toption: opt,\n\t\t\t\t\t\tcommand,\n\t\t\t\t\t\toffender: {\n\t\t\t\t\t\t\tnamePart,\n\t\t\t\t\t\t\tdataPart,\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tif (opt.enumVals && !opt.enumVals.find((val) => val === dataPart)) {\n\t\t\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\t\t\ttype: 'error',\n\t\t\t\t\t\tname: cliName,\n\t\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\t\tviolation: 'enum_violation',\n\t\t\t\t\t\toption: opt,\n\t\t\t\t\t\tcommand,\n\t\t\t\t\t\toffender: {\n\t\t\t\t\t\t\tnamePart,\n\t\t\t\t\t\t\tdataPart,\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tdata = dataPart;\n\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (!hasEq && nextArg === undefined) {\n\t\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\t\ttype: 'error',\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tviolation: 'invalid_number_syntax',\n\t\t\t\t\toption: opt,\n\t\t\t\t\tcommand,\n\t\t\t\t\toffender: {\n\t\t\t\t\t\tnamePart,\n\t\t\t\t\t\tdataPart,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst numData = Number(dataPart);\n\n\t\t\tif (isNaN(numData)) {\n\t\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\t\ttype: 'error',\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tviolation: 'invalid_number_value',\n\t\t\t\t\toption: opt,\n\t\t\t\t\tcommand,\n\t\t\t\t\toffender: {\n\t\t\t\t\t\tnamePart,\n\t\t\t\t\t\tdataPart,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (opt.isInt && !isInt(numData)) {\n\t\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\t\ttype: 'error',\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tviolation: 'expected_int',\n\t\t\t\t\toption: opt,\n\t\t\t\t\tcommand,\n\t\t\t\t\toffender: {\n\t\t\t\t\t\tnamePart,\n\t\t\t\t\t\tdataPart,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (opt.minVal !== undefined && numData < opt.minVal) {\n\t\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\t\ttype: 'error',\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tviolation: 'below_min',\n\t\t\t\t\toption: opt,\n\t\t\t\t\tcommand,\n\t\t\t\t\toffender: {\n\t\t\t\t\t\tnamePart,\n\t\t\t\t\t\tdataPart,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (opt.maxVal !== undefined && numData > opt.maxVal) {\n\t\t\t\tthrow new BroCliError(undefined, {\n\t\t\t\t\ttype: 'error',\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tviolation: 'above_max',\n\t\t\t\t\toption: opt,\n\t\t\t\t\tcommand,\n\t\t\t\t\toffender: {\n\t\t\t\t\t\tnamePart,\n\t\t\t\t\t\tdataPart,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tdata = numData;\n\n\t\t\treturn true;\n\t\t}\n\t});\n\n\treturn {\n\t\tdata,\n\t\tskipNext,\n\t\tname: option?.[0],\n\t\toption: option?.[1],\n\t};\n};\n\nconst parseOptions = (\n\tcommand: Command,\n\targs: string[],\n\tcliName: string | undefined,\n\tcliDescription: string | undefined,\n\tomitKeysOfUndefinedOptions?: boolean,\n): Record<string, OutputType> | 'help' | 'version' | undefined => {\n\tconst options = command.options;\n\n\tconst optEntries = Object.entries(options ?? {} as Exclude<typeof options, undefined>).map(\n\t\t(opt) => [opt[0], opt[1].config] as [string, ProcessedBuilderConfig],\n\t);\n\n\tconst nonPositionalEntries = optEntries.filter(([key, opt]) => opt.type !== 'positional');\n\tconst positionalEntries = optEntries.filter(([key, opt]) => opt.type === 'positional');\n\n\tconst result: Record<string, OutputType> = {};\n\n\tconst missingRequiredArr: string[][] = [];\n\tconst unrecognizedArgsArr: string[] = [];\n\n\tfor (let i = 0; i < args.length; ++i) {\n\t\tconst arg = args[i]!;\n\t\tconst nextArg = args[i + 1];\n\n\t\tconst {\n\t\t\tdata,\n\t\t\tname,\n\t\t\toption,\n\t\t\tskipNext,\n\t\t\tisHelp,\n\t\t\tisVersion,\n\t\t} = parseArg(command, nonPositionalEntries, positionalEntries, arg, nextArg, cliName, cliDescription);\n\t\tif (!option) unrecognizedArgsArr.push(arg.split('=')[0]!);\n\t\tif (skipNext) ++i;\n\n\t\tif (isHelp) return 'help';\n\t\tif (isVersion) return 'version';\n\n\t\tresult[name!] = data;\n\t}\n\n\tfor (const [optKey, option] of optEntries) {\n\t\tconst data = result[optKey] ?? option.default;\n\n\t\tif (!omitKeysOfUndefinedOptions) {\n\t\t\tresult[optKey] = data;\n\t\t} else {\n\t\t\tif (data !== undefined) result[optKey] = data;\n\t\t}\n\n\t\tif (option.isRequired && result[optKey] === undefined) missingRequiredArr.push([option.name!, ...option.aliases]);\n\t}\n\n\tif (missingRequiredArr.length) {\n\t\tthrow new BroCliError(undefined, {\n\t\t\ttype: 'error',\n\t\t\tviolation: 'missing_args_error',\n\t\t\tname: cliName,\n\t\t\tdescription: cliDescription,\n\t\t\tcommand,\n\t\t\tmissing: missingRequiredArr as [string[], ...string[][]],\n\t\t});\n\t}\n\tif (unrecognizedArgsArr.length) {\n\t\tthrow new BroCliError(undefined, {\n\t\t\ttype: 'error',\n\t\t\tviolation: 'unrecognized_args_error',\n\t\t\tname: cliName,\n\t\t\tdescription: cliDescription,\n\t\t\tcommand,\n\t\t\tunrecognized: unrecognizedArgsArr as [string, ...string[]],\n\t\t});\n\t}\n\n\treturn Object.keys(result).length ? result : undefined;\n};\n\nexport const getCommandNameWithParents = (command: Command): string =>\n\tcommand.parent ? `${getCommandNameWithParents(command.parent)} ${command.name}` : command.name;\n\nconst validateCommands = (commands: Command[], parent?: Command) => {\n\tconst storedNames: Record<string, [string, ...string[]]> = {};\n\n\tfor (const cmd of commands) {\n\t\tconst storageVals = Object.values(storedNames);\n\n\t\tfor (const storage of storageVals) {\n\t\t\tconst nameOccupier = storage.find((e) => e === cmd.name);\n\n\t\t\tif (!nameOccupier) continue;\n\n\t\t\tthrow new BroCliError(\n\t\t\t\t`Can't define command '${getCommandNameWithParents(cmd)}': name is already in use by command '${\n\t\t\t\t\tparent ? `${getCommandNameWithParents(parent)} ` : ''\n\t\t\t\t}${storage[0]}'!`,\n\t\t\t);\n\t\t}\n\n\t\tif (cmd.aliases) {\n\t\t\tfor (const alias of cmd.aliases) {\n\t\t\t\tfor (const storage of storageVals) {\n\t\t\t\t\tconst nameOccupier = storage.find((e) => e === alias);\n\n\t\t\t\t\tif (!nameOccupier) continue;\n\n\t\t\t\t\tthrow new BroCliError(\n\t\t\t\t\t\t`Can't define command '${getCommandNameWithParents(cmd)}': alias '${alias}' is already in use by command '${\n\t\t\t\t\t\t\tparent ? `${getCommandNameWithParents(parent)} ` : ''\n\t\t\t\t\t\t}${storage[0]}'!`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tstoredNames[cmd.name] = cmd.aliases\n\t\t\t? [cmd.name, ...cmd.aliases]\n\t\t\t: [cmd.name];\n\n\t\tif (cmd.subcommands) cmd.subcommands = validateCommands(cmd.subcommands, cmd) as [Command, ...Command[]];\n\t}\n\n\treturn commands;\n};\n\nconst removeByIndex = <T>(arr: T[], idx: number): T[] => [...arr.slice(0, idx), ...arr.slice(idx + 1, arr.length)];\n\n/**\n * Runs CLI commands\n *\n * @param commands - command collection\n *\n * @param config - additional settings\n */\nexport const run = async (commands: Command[], config?: BroCliConfig): Promise<void> => {\n\tconst eventHandler = config?.theme\n\t\t? eventHandlerWrapper(config.theme)\n\t\t: defaultEventHandler;\n\tconst argSource = config?.argSource ?? process.argv;\n\tconst version = config?.version;\n\tconst help = config?.help;\n\tconst omitKeysOfUndefinedOptions = config?.omitKeysOfUndefinedOptions ?? false;\n\tconst cliName = config?.name;\n\tconst cliDescription = config?.description;\n\n\ttry {\n\t\tconst processedCmds = validateCommands(commands);\n\n\t\tlet args = argSource.slice(2, argSource.length);\n\t\tif (!args.length) {\n\t\t\treturn help !== undefined ? await executeOrLog(help) : await eventHandler({\n\t\t\t\ttype: 'global_help',\n\t\t\t\tdescription: cliDescription,\n\t\t\t\tname: cliName,\n\t\t\t\tcommands: processedCmds,\n\t\t\t});\n\t\t}\n\n\t\tconst helpIndex = args.findIndex((arg) => arg === '--help' || arg === '-h');\n\t\tif (\n\t\t\thelpIndex !== -1 && (helpIndex > 0\n\t\t\t\t? args[helpIndex - 1]?.startsWith('-') && !args[helpIndex - 1]!.includes('=') ? false : true\n\t\t\t\t: true)\n\t\t) {\n\t\t\tconst command = getCommand(processedCmds, args, cliName, cliDescription).command;\n\n\t\t\tif (typeof command === 'object') {\n\t\t\t\treturn command.help !== undefined ? await executeOrLog(command.help) : await eventHandler({\n\t\t\t\t\ttype: 'command_help',\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tcommand: command,\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\treturn help !== undefined ? await executeOrLog(help) : await eventHandler({\n\t\t\t\t\ttype: 'global_help',\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tcommands: processedCmds,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tconst versionIndex = args.findIndex((arg) => arg === '--version' || arg === '-v');\n\t\tif (versionIndex !== -1 && (versionIndex > 0 ? args[versionIndex - 1]?.startsWith('-') ? false : true : true)) {\n\t\t\treturn version !== undefined ? await executeOrLog(version) : await eventHandler({\n\t\t\t\ttype: 'version',\n\t\t\t\tname: cliName,\n\t\t\t\tdescription: cliDescription,\n\t\t\t});\n\t\t}\n\n\t\tconst { command, args: newArgs } = getCommand(processedCmds, args, cliName, cliDescription);\n\n\t\tif (!command) {\n\t\t\treturn help !== undefined ? await executeOrLog(help) : await eventHandler({\n\t\t\t\ttype: 'global_help',\n\t\t\t\tdescription: cliDescription,\n\t\t\t\tname: cliName,\n\t\t\t\tcommands: processedCmds,\n\t\t\t});\n\t\t}\n\n\t\tif (command === 'help') {\n\t\t\tlet helpCommand: Command | 'help' | undefined;\n\t\t\tlet newestArgs: string[] = newArgs;\n\n\t\t\tdo {\n\t\t\t\tconst res = getCommand(processedCmds, newestArgs, cliName, cliDescription);\n\t\t\t\thelpCommand = res.command;\n\t\t\t\tnewestArgs = res.args;\n\t\t\t} while (helpCommand === 'help');\n\n\t\t\treturn helpCommand\n\t\t\t\t? helpCommand.help !== undefined ? await executeOrLog(helpCommand.help) : await eventHandler({\n\t\t\t\t\ttype: 'command_help',\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tcommand: helpCommand,\n\t\t\t\t})\n\t\t\t\t: help !== undefined\n\t\t\t\t? await executeOrLog(help)\n\t\t\t\t: await eventHandler({\n\t\t\t\t\ttype: 'global_help',\n\t\t\t\t\tdescription: cliDescription,\n\t\t\t\t\tname: cliName,\n\t\t\t\t\tcommands: processedCmds,\n\t\t\t\t});\n\t\t}\n\n\t\tconst optionResult = parseOptions(command, newArgs, cliName, cliDescription, omitKeysOfUndefinedOptions);\n\n\t\tif (optionResult === 'help') {\n\t\t\treturn command.help !== undefined ? await executeOrLog(command.help) : await eventHandler({\n\t\t\t\ttype: 'command_help',\n\t\t\t\tdescription: cliDescription,\n\t\t\t\tname: cliName,\n\t\t\t\tcommand: command,\n\t\t\t});\n\t\t}\n\t\tif (optionResult === 'version') {\n\t\t\treturn version !== undefined ? await executeOrLog(version) : await eventHandler({\n\t\t\t\ttype: 'version',\n\t\t\t\tname: cliName,\n\t\t\t\tdescription: cliDescription,\n\t\t\t});\n\t\t}\n\n\t\tif (command.handler) {\n\t\t\tif (config?.hook) await config.hook('before', command);\n\t\t\tawait command.handler(command.transform ? await command.transform(optionResult) : optionResult);\n\t\t\tif (config?.hook) await config.hook('after', command);\n\t\t\treturn;\n\t\t} else {\n\t\t\treturn command.help !== undefined ? await executeOrLog(command.help) : await eventHandler({\n\t\t\t\ttype: 'command_help',\n\t\t\t\tdescription: cliDescription,\n\t\t\t\tname: cliName,\n\t\t\t\tcommand: command,\n\t\t\t});\n\t\t}\n\t} catch (e) {\n\t\tif (e instanceof BroCliError) {\n\t\t\tif (e.event) await eventHandler(e.event);\n\t\t\telse {\n\t\t\t\t// @ts-expect-error - option meant only for tests\n\t\t\t\tif (!config?.noExit) console.error(e.message);\n\t\t\t\t// @ts-expect-error - return path meant only for tests\n\t\t\t\telse return e.message;\n\t\t\t}\n\t\t} else {\n\t\t\tawait eventHandler({\n\t\t\t\ttype: 'error',\n\t\t\t\tviolation: 'unknown_error',\n\t\t\t\tname: cliName,\n\t\t\t\tdescription: cliDescription,\n\t\t\t\terror: e,\n\t\t\t});\n\t\t}\n\n\t\t// @ts-expect-error - option meant only for tests\n\t\tif (!config?.noExit) process.exit(1);\n\n\t\treturn;\n\t}\n};\n\nexport const handler = <TOpts extends Record<string, GenericBuilderInternals>>(\n\toptions: TOpts,\n\thandler: CommandHandler<TOpts>,\n) => handler;\n\nexport const test = async <TOpts, THandlerInput>(\n\tcommand: Command<TOpts, THandlerInput>,\n\targs: string,\n): Promise<TestResult<THandlerInput>> => {\n\ttry {\n\t\tconst cliParsedArgs: string[] = shellArgs(args);\n\t\tconst options = parseOptions(command, cliParsedArgs, undefined, undefined);\n\n\t\tif (options === 'help' || options === 'version') {\n\t\t\treturn {\n\t\t\t\ttype: options,\n\t\t\t};\n\t\t}\n\n\t\treturn {\n\t\t\toptions: command.transform ? await command.transform(options) : options,\n\t\t\ttype: 'handler',\n\t\t};\n\t} catch (e) {\n\t\treturn {\n\t\t\ttype: 'error',\n\t\t\terror: e,\n\t\t};\n\t}\n};\n\nexport const commandsInfo = (\n\tcommands: Command[],\n): CommandsInfo => {\n\tconst validated = validateCommands(commands);\n\n\treturn Object.fromEntries(validated.map((c) => [c.name, {\n\t\tname: c.name,\n\t\taliases: clone(c.aliases),\n\t\tdesc: c.desc,\n\t\tshortDesc: c.shortDesc,\n\t\tisHidden: c.hidden,\n\t\toptions: c.options\n\t\t\t? Object.fromEntries(Object.entries(c.options).map(([key, opt]) => [key, clone(opt.config)]))\n\t\t\t: undefined,\n\t\tmetadata: clone(c.metadata),\n\t\tsubcommands: c.subcommands ? commandsInfo(c.subcommands) : undefined,\n\t}]));\n};\n", "import { type Command, command, getCommandNameWithParents } from './command-core';\nimport type { BuilderConfig, ProcessedBuilderConfig } from './option-builder';\n\nexport type CommandHelpEvent = {\n\ttype: 'command_help';\n\tname: string | undefined;\n\tdescription: string | undefined;\n\tcommand: Command;\n};\n\nexport type GlobalHelpEvent = {\n\ttype: 'global_help';\n\tdescription: string | undefined;\n\tname: string | undefined;\n\tcommands: Command[];\n};\n\nexport type MissingArgsEvent = {\n\ttype: 'error';\n\tviolation: 'missing_args_error';\n\tname: string | undefined;\n\tdescription: string | undefined;\n\tcommand: Command;\n\tmissing: [string[], ...string[][]];\n};\n\nexport type UnrecognizedArgsEvent = {\n\ttype: 'error';\n\tviolation: 'unrecognized_args_error';\n\tname: string | undefined;\n\tdescription: string | undefined;\n\tcommand: Command;\n\tunrecognized: [string, ...string[]];\n};\n\nexport type UnknownCommandEvent = {\n\ttype: 'error';\n\tviolation: 'unknown_command_error';\n\tname: string | undefined;\n\tdescription: string | undefined;\n\tcommands: Command[];\n\toffender: string;\n};\n\nexport type UnknownSubcommandEvent = {\n\ttype: 'error';\n\tviolation: 'unknown_subcommand_error';\n\tname: string | undefined;\n\tdescription: string | undefined;\n\tcommand: Command;\n\toffender: string;\n};\n\nexport type UnknownErrorEvent = {\n\ttype: 'error';\n\tviolation: 'unknown_error';\n\tname: string | undefined;\n\tdescription: string | undefined;\n\terror: unknown;\n};\n\nexport type VersionEvent = {\n\ttype: 'version';\n\tname: string | undefined;\n\tdescription: string | undefined;\n};\n\nexport type GenericValidationViolation =\n\t| 'above_max'\n\t| 'below_min'\n\t| 'expected_int'\n\t| 'invalid_boolean_syntax'\n\t| 'invalid_string_syntax'\n\t| 'invalid_number_syntax'\n\t| 'invalid_number_value'\n\t| 'enum_violation';\n\nexport type ValidationViolation = BroCliEvent extends infer Event\n\t? Event extends { violation: string } ? Event['violation'] : never\n\t: never;\n\nexport type ValidationErrorEvent = {\n\ttype: 'error';\n\tviolation: GenericValidationViolation;\n\tname: string | undefined;\n\tdescription: string | undefined;\n\tcommand: Command;\n\toption: ProcessedBuilderConfig;\n\toffender: {\n\t\tnamePart?: string;\n\t\tdataPart?: string;\n\t};\n};\n\nexport type BroCliEvent =\n\t| CommandHelpEvent\n\t| GlobalHelpEvent\n\t| MissingArgsEvent\n\t| UnrecognizedArgsEvent\n\t| UnknownCommandEvent\n\t| UnknownSubcommandEvent\n\t| ValidationErrorEvent\n\t| VersionEvent\n\t| UnknownErrorEvent;\n\nexport type BroCliEventType = BroCliEvent['type'];\n\nconst getOptionTypeText = (option: BuilderConfig) => {\n\tlet result = '';\n\n\tswitch (option.type) {\n\t\tcase 'boolean':\n\t\t\tresult = '';\n\t\t\tbreak;\n\t\tcase 'number': {\n\t\t\tif ((option.minVal ?? option.maxVal) !== undefined) {\n\t\t\t\tlet text = '';\n\n\t\t\t\tif (option.isInt) text = text + `integer `;\n\n\t\t\t\tif (option.minVal !== undefined) text = text + `[${option.minVal};`;\n\t\t\t\telse text = text + `(∞;`;\n\n\t\t\t\tif (option.maxVal !== undefined) text = text + `${option.maxVal}]`;\n\t\t\t\telse text = text + `∞)`;\n\n\t\t\t\tresult = text;\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tif (option.isInt) {\n\t\t\t\tresult = 'integer';\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tresult = 'number';\n\t\t\tbreak;\n\t\t}\n\t\tcase 'string': {\n\t\t\tif (option.enumVals) {\n\t\t\t\tresult = '[ ' + option.enumVals.join(' | ') + ' ]';\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tresult = 'string';\n\t\t\tbreak;\n\t\t}\n\t\tcase 'positional': {\n\t\t\tresult = `${option.isRequired ? '<' : '['}${option.enumVals ? option.enumVals.join('|') : option.name}${\n\t\t\t\toption.isRequired ? '>' : ']'\n\t\t\t}`;\n\t\t\tbreak;\n\t\t}\n\t}\n\n\tif (option.isRequired && option.type !== 'positional') result = '!' + result.length ? ' ' : '' + result;\n\treturn result;\n};\n\n/**\n * Return `true` if your handler processes the event\n *\n * Return `false` to process event with a built-in handler\n */\nexport type EventHandler = (event: BroCliEvent) => boolean | Promise<boolean>;\nexport const defaultEventHandler: EventHandler = async (event) => {\n\tswitch (event.type) {\n\t\tcase 'command_help': {\n\t\t\tconst command = event.command;\n\t\t\tconst commandName = getCommandNameWithParents(command);\n\t\t\tconst cliName = event.name;\n\t\t\tconst desc = command.desc ?? command.shortDesc;\n\t\t\tconst subs = command.subcommands?.filter((s) => !s.hidden);\n\t\t\tconst subcommands = subs && subs.length ? subs : undefined;\n\n\t\t\tif (desc !== undefined) {\n\t\t\t\tconsole.log(`\\n${desc}`);\n\t\t\t}\n\n\t\t\tconst opts = Object.values(command.options ?? {} as Exclude<typeof command.options, undefined>).filter((opt) =>\n\t\t\t\t!opt.config.isHidden\n\t\t\t);\n\t\t\tconst positionals = opts.filter((opt) => opt.config.type === 'positional');\n\t\t\tconst options = opts.filter((opt) => opt.config.type !== 'positional');\n\n\t\t\tconsole.log('\\nUsage:');\n\t\t\tif (command.handler) {\n\t\t\t\tconsole.log(\n\t\t\t\t\t`  ${cliName ? cliName + ' ' : ''}${commandName}${\n\t\t\t\t\t\tpositionals.length\n\t\t\t\t\t\t\t? ' '\n\t\t\t\t\t\t\t\t+ positionals.map(({ config: p }) => getOptionTypeText(p)).join(' ')\n\t\t\t\t\t\t\t: ''\n\t\t\t\t\t} [flags]`,\n\t\t\t\t);\n\t\t\t} else console.log(`  ${cliName ? cliName + ' ' : ''}${commandName} [command]`);\n\n\t\t\tif (command.aliases) {\n\t\t\t\tconsole.log(`\\nAliases:`);\n\t\t\t\tconsole.log(`  ${[command.name, ...command.aliases].join(', ')}`);\n\t\t\t}\n\n\t\t\tif (subcommands) {\n\t\t\t\tconsole.log('\\nAvailable Commands:');\n\t\t\t\tconst padding = 3;\n\t\t\t\tconst maxLength = subcommands.reduce((p, e) => e.name.length > p ? e.name.length : p, 0);\n\t\t\t\tconst paddedLength = maxLength + padding;\n\t\t\t\tconst preDescPad = 2 + paddedLength;\n\n\t\t\t\tconst data = subcommands.map((s) =>\n\t\t\t\t\t`  ${s.name.padEnd(paddedLength)}${\n\t\t\t\t\t\t(() => {\n\t\t\t\t\t\t\tconst description = s.shortDesc ?? s.desc;\n\t\t\t\t\t\t\tif (!description?.length) return '';\n\t\t\t\t\t\t\tconst split = description.split('\\n');\n\t\t\t\t\t\t\tconst first = split.shift()!;\n\n\t\t\t\t\t\t\tconst final = [first, ...split.map((s) => ''.padEnd(preDescPad) + s)].join('\\n');\n\n\t\t\t\t\t\t\treturn final;\n\t\t\t\t\t\t})()\n\t\t\t\t\t}`\n\t\t\t\t)\n\t\t\t\t\t.join('\\n');\n\t\t\t\tconsole.log(data);\n\t\t\t}\n\n\t\t\tif (options.length) {\n\t\t\t\tconst aliasLength = options.reduce((p, e) => {\n\t\t\t\t\tconst currentLength = e.config.aliases.reduce((pa, a) => pa + a.length, 0)\n\t\t\t\t\t\t+ ((e.config.aliases.length - 1) * 2) + 1; // Names + coupling symbols \", \" + ending coma\n\n\t\t\t\t\treturn currentLength > p ? currentLength : p;\n\t\t\t\t}, 0);\n\t\t\t\tconst paddedAliasLength = aliasLength > 0 ? aliasLength + 1 : 0;\n\t\t\t\tconst nameLength = options.reduce((p, e) => {\n\t\t\t\t\tconst typeLen = getOptionTypeText(e.config).length;\n\t\t\t\t\tconst length = typeLen > 0 ? e.config.name.length + 1 + typeLen : e.config.name.length;\n\n\t\t\t\t\treturn length > p ? length : p;\n\t\t\t\t}, 0) + 3;\n\n\t\t\t\tconst preDescPad = paddedAliasLength + nameLength + 2;\n\n\t\t\t\tconst data = options.map(({ config: opt }) =>\n\t\t\t\t\t`  ${`${opt.aliases.length ? opt.aliases.join(', ') + ',' : ''}`.padEnd(paddedAliasLength)}${\n\t\t\t\t\t\t`${opt.name}${\n\t\t\t\t\t\t\t(() => {\n\t\t\t\t\t\t\t\tconst typeText = getOptionTypeText(opt);\n\t\t\t\t\t\t\t\treturn typeText.length ? ' ' + typeText : '';\n\t\t\t\t\t\t\t})()\n\t\t\t\t\t\t}`.padEnd(nameLength)\n\t\t\t\t\t}${\n\t\t\t\t\t\t(() => {\n\t\t\t\t\t\t\tif (!opt.description?.length) {\n\t\t\t\t\t\t\t\treturn opt.default !== undefined\n\t\t\t\t\t\t\t\t\t? `default: ${JSON.stringify(opt.default)}`\n\t\t\t\t\t\t\t\t\t: '';\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tconst split = opt.description.split('\\n');\n\t\t\t\t\t\t\tconst first = split.shift()!;\n\t\t\t\t\t\t\tconst def = opt.default !== undefined ? ` (default: ${JSON.stringify(opt.default)})` : '';\n\n\t\t\t\t\t\t\tconst final = [first, ...split.map((s) => ''.padEnd(preDescPad) + s)].join('\\n') + def;\n\n\t\t\t\t\t\t\treturn final;\n\t\t\t\t\t\t})()\n\t\t\t\t\t}`\n\t\t\t\t).join('\\n');\n\n\t\t\t\tconsole.log('\\nFlags:');\n\t\t\t\tconsole.log(data);\n\t\t\t}\n\n\t\t\tconsole.log('\\nGlobal flags:');\n\t\t\tconsole.log(`  -h, --help      help for ${commandName}`);\n\t\t\tconsole.log(`  -v, --version   version${cliName ? ` for ${cliName}` : ''}`);\n\n\t\t\tif (subcommands) {\n\t\t\t\tconsole.log(\n\t\t\t\t\t`\\nUse \"${\n\t\t\t\t\t\tcliName ? cliName + ' ' : ''\n\t\t\t\t\t}${commandName} [command] --help\" for more information about a command.\\n`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t}\n\n\t\tcase 'global_help': {\n\t\t\tconst cliName = event.name;\n\t\t\tconst desc = event.description;\n\t\t\tconst commands = event.commands.filter((c) => !c.hidden);\n\n\t\t\tif (desc !== undefined) {\n\t\t\t\tconsole.log(`${desc}\\n`);\n\t\t\t}\n\n\t\t\tconsole.log('Usage:');\n\t\t\tconsole.log(`  ${cliName ? cliName + ' ' : ''}[command]`);\n\n\t\t\tif (commands.length) {\n\t\t\t\tconsole.log('\\nAvailable Commands:');\n\t\t\t\tconst padding = 3;\n\t\t\t\tconst maxLength = commands.reduce((p, e) => e.name.length > p ? e.name.length : p, 0);\n\t\t\t\tconst paddedLength = maxLength + padding;\n\n\t\t\t\tconst data = commands.map((c) =>\n\t\t\t\t\t`  ${c.name.padEnd(paddedLength)}${\n\t\t\t\t\t\t(() => {\n\t\t\t\t\t\t\tconst desc = c.shortDesc ?? c.desc;\n\n\t\t\t\t\t\t\tif (!desc?.length) return '';\n\n\t\t\t\t\t\t\tconst split = desc.split('\\n');\n\t\t\t\t\t\t\tconst first = split.shift()!;\n\n\t\t\t\t\t\t\tconst final = [first, ...split.map((s) => ''.padEnd(paddedLength + 2) + s)].join('\\n');\n\n\t\t\t\t\t\t\treturn final;\n\t\t\t\t\t\t})()\n\t\t\t\t\t}`\n\t\t\t\t)\n\t\t\t\t\t.join('\\n');\n\t\t\t\tconsole.log(data);\n\t\t\t} else {\n\t\t\t\tconsole.log('\\nNo available commands.');\n\t\t\t}\n\n\t\t\tconsole.log('\\nFlags:');\n\t\t\tconsole.log(`  -h, --help      help${cliName ? ` for ${cliName}` : ''}`);\n\t\t\tconsole.log(`  -v, --version   version${cliName ? ` for ${cliName}` : ''}`);\n\t\t\tconsole.log('\\n');\n\n\t\t\treturn true;\n\t\t}\n\n\t\tcase 'version': {\n\t\t\treturn true;\n\t\t}\n\n\t\tcase 'error': {\n\t\t\tlet msg: string;\n\n\t\t\tswitch (event.violation) {\n\t\t\t\tcase 'above_max': {\n\t\t\t\t\tconst matchedName = event.offender.namePart;\n\t\t\t\t\tconst data = event.offender.dataPart;\n\t\t\t\t\tconst option = event.option;\n\n\t\t\t\t\tconst max = option.maxVal!;\n\t\t\t\t\tmsg =\n\t\t\t\t\t\t`Invalid value: number type argument '${matchedName}' expects maximal value of ${max} as an input, got: ${data}`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'below_min': {\n\t\t\t\t\tconst matchedName = event.offender.namePart;\n\t\t\t\t\tconst data = event.offender.dataPart;\n\t\t\t\t\tconst option = event.option;\n\n\t\t\t\t\tconst min = option.minVal;\n\n\t\t\t\t\tmsg =\n\t\t\t\t\t\t`Invalid value: number type argument '${matchedName}' expects minimal value of ${min} as an input, got: ${data}`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'expected_int': {\n\t\t\t\t\tconst matchedName = event.offender.namePart;\n\t\t\t\t\tconst data = event.offender.dataPart;\n\n\t\t\t\t\tmsg = `Invalid value: number type argument '${matchedName}' expects an integer as an input, got: ${data}`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'invalid_boolean_syntax': {\n\t\t\t\t\tconst matchedName = event.offender.namePart;\n\t\t\t\t\tconst data = event.offender.dataPart;\n\n\t\t\t\t\tmsg =\n\t\t\t\t\t\t`Invalid syntax: boolean type argument '${matchedName}' must have it's value passed in the following formats: ${matchedName}=<value> | ${matchedName} <value> | ${matchedName}.\\nAllowed values: true, false, 0, 1`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'invalid_string_syntax': {\n\t\t\t\t\tconst matchedName = event.offender.namePart;\n\n\t\t\t\t\tmsg =\n\t\t\t\t\t\t`Invalid syntax: string type argument '${matchedName}' must have it's value passed in the following formats: ${matchedName}=<value> | ${matchedName} <value>`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'invalid_number_syntax': {\n\t\t\t\t\tconst matchedName = event.offender.namePart;\n\n\t\t\t\t\tmsg =\n\t\t\t\t\t\t`Invalid syntax: number type argument '${matchedName}' must have it's value passed in the following formats: ${matchedName}=<value> | ${matchedName} <value>`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'invalid_number_value': {\n\t\t\t\t\tconst matchedName = event.offender.namePart;\n\t\t\t\t\tconst data = event.offender.dataPart;\n\n\t\t\t\t\tmsg = `Invalid value: number type argument '${matchedName}' expects a number as an input, got: ${data}`;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'enum_violation': {\n\t\t\t\t\tconst matchedName = event.offender.namePart;\n\t\t\t\t\tconst data = event.offender.dataPart;\n\t\t\t\t\tconst option = event.option;\n\n\t\t\t\t\tconst values = option.enumVals!;\n\n\t\t\t\t\tmsg = option.type === 'positional'\n\t\t\t\t\t\t? `Invalid value: value for the positional argument '${option.name}' must be either one of the following: ${\n\t\t\t\t\t\t\tvalues.join(', ')\n\t\t\t\t\t\t}; Received: ${data}`\n\t\t\t\t\t\t: `Invalid value: value for the argument '${matchedName}' must be either one of the following: ${\n\t\t\t\t\t\t\tvalues.join(', ')\n\t\t\t\t\t\t}; Received: ${data}`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'unknown_command_error': {\n\t\t\t\t\tconst msg = `Unknown command: '${event.offender}'.\\nType '--help' to get help on the cli.`;\n\n\t\t\t\t\tconsole.error(msg);\n\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tcase 'unknown_subcommand_error': {\n\t\t\t\t\tconst cName = getCommandNameWithParents(event.command);\n\t\t\t\t\tconst msg =\n\t\t\t\t\t\t`Unknown command: ${cName} ${event.offender}.\\nType '${cName} --help' to get the help on command.`;\n\n\t\t\t\t\tconsole.error(msg);\n\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tcase 'missing_args_error': {\n\t\t\t\t\tconst { missing: missingOpts, command } = event;\n\n\t\t\t\t\tmsg = `Command '${command.name}' is missing following required options: ${\n\t\t\t\t\t\tmissingOpts.map((opt) => {\n\t\t\t\t\t\t\tconst name = opt.shift()!;\n\t\t\t\t\t\t\tconst aliases = opt;\n\n\t\t\t\t\t\t\tif (aliases.length) return `${name} [${aliases.join(', ')}]`;\n\n\t\t\t\t\t\t\treturn name;\n\t\t\t\t\t\t}).join(', ')\n\t\t\t\t\t}`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'unrecognized_args_error': {\n\t\t\t\t\tconst { command, unrecognized } = event;\n\t\t\t\t\tmsg = `Unrecognized options for command '${command.name}': ${unrecognized.join(', ')}`;\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tcase 'unknown_error': {\n\t\t\t\t\tconst e = event.error;\n\t\t\t\t\tconsole.error(typeof e === 'object' && e !== null && 'message' in e ? e.message : e);\n\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconsole.error(msg);\n\n\t\t\treturn true;\n\t\t}\n\t}\n\n\t// @ts-expect-error\n\treturn false;\n};\n\nexport const eventHandlerWrapper = (customEventHandler: EventHandler) => async (event: BroCliEvent) =>\n\tawait customEventHandler(event) ? true : await defaultEventHandler(event);\n", "import { parse as parseQuotes } from 'shell-quote';\n\nexport function isInt(value: number) {\n\treturn value === Math.floor(value);\n}\n\nexport const shellArgs = (str: string) => parseQuotes(str).map((e) => e.toString());\n\nexport const executeOrLog = async (target?: string | Function) =>\n\ttypeof target === 'string' ? console.log(target) : target ? await target() : undefined;\n", "import { BroCliError } from './brocli-error';\n\nexport type OptionType = 'string' | 'boolean' | 'number' | 'positional';\n\nexport type OutputType = string | boolean | number | undefined;\n\nexport type BuilderConfig<TType extends OptionType = OptionType> = {\n\tname?: string | undefined;\n\taliases: string[];\n\ttype: TType;\n\tdescription?: string;\n\tdefault?: OutputType;\n\tisHidden?: boolean;\n\tisRequired?: boolean;\n\tisInt?: boolean;\n\tminVal?: number;\n\tmaxVal?: number;\n\tenumVals?: [string, ...string[]];\n};\n\nexport type ProcessedBuilderConfig = {\n\tname: string;\n\taliases: string[];\n\ttype: OptionType;\n\tdescription?: string;\n\tdefault?: OutputType;\n\tisHidden?: boolean;\n\tisRequired?: boolean;\n\tisInt?: boolean;\n\tminVal?: number;\n\tmaxVal?: number;\n\tenumVals?: [string, ...string[]];\n};\n\nexport type BuilderConfigLimited = BuilderConfig & {\n\ttype: Exclude<OptionType, 'positional'>;\n};\n\nexport class OptionBuilderBase<\n\tTBuilderConfig extends BuilderConfig = BuilderConfig,\n\tTOutput extends OutputType = string,\n\tTOmit extends string = '',\n\tTEnums extends string | undefined = undefined,\n> {\n\tpublic _: {\n\t\tconfig: TBuilderConfig;\n\t\t/**\n\t\t * Type-level only field\n\t\t *\n\t\t * Do not attempt to access at a runtime\n\t\t */\n\t\t$output: TOutput;\n\t};\n\n\tprivate config = (): TBuilderConfig => this._.config;\n\n\tconstructor(config?: TBuilderConfig) {\n\t\tthis._ = {\n\t\t\tconfig: config ?? {\n\t\t\t\taliases: [],\n\t\t\t\ttype: 'string',\n\t\t\t} as unknown as TBuilderConfig,\n\t\t\t$output: undefined as any as TOutput,\n\t\t};\n\t}\n\n\tpublic string<TName extends string>(name: TName): Omit<\n\t\tOptionBuilderBase<\n\t\t\tBuilderConfig<'string'>,\n\t\t\tstring | undefined,\n\t\t\tTOmit | OptionType | 'min' | 'max' | 'int'\n\t\t>,\n\t\tTOmit | OptionType | 'min' | 'max' | 'int'\n\t>;\n\tpublic string(): Omit<\n\t\tOptionBuilderBase<\n\t\t\tBuilderConfig<'string'>,\n\t\t\tstring | undefined,\n\t\t\tTOmit | OptionType | 'min' | 'max' | 'int'\n\t\t>,\n\t\tTOmit | OptionType | 'min' | 'max' | 'int'\n\t>;\n\tpublic string(\n\t\tname?: string,\n\t) {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, type: 'string', name: name }) as any;\n\t}\n\n\tpublic number<TName extends string>(name: TName): Omit<\n\t\tOptionBuilderBase<\n\t\t\tBuilderConfig<'number'>,\n\t\t\tnumber | undefined,\n\t\t\tTOmit | OptionType | 'enum'\n\t\t>,\n\t\tTOmit | OptionType | 'enum'\n\t>;\n\tpublic number(): Omit<\n\t\tOptionBuilderBase<\n\t\t\tBuilderConfig<'number'>,\n\t\t\tstring | undefined,\n\t\t\tTOmit | OptionType | 'enum'\n\t\t>,\n\t\tTOmit | OptionType | 'enum'\n\t>;\n\tpublic number(\n\t\tname?: string,\n\t) {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, type: 'number', name: name }) as any;\n\t}\n\n\tpublic boolean<TName extends string>(name: TName): Omit<\n\t\tOptionBuilderBase<\n\t\t\tBuilderConfig<'boolean'>,\n\t\t\tboolean | undefined,\n\t\t\tTOmit | OptionType | 'min' | 'max' | 'enum' | 'int'\n\t\t>,\n\t\tTOmit | OptionType | 'min' | 'max' | 'enum' | 'int'\n\t>;\n\tpublic boolean(): Omit<\n\t\tOptionBuilderBase<\n\t\t\tBuilderConfig<'boolean'>,\n\t\t\tboolean | undefined,\n\t\t\tTOmit | OptionType | 'min' | 'max' | 'enum' | 'int'\n\t\t>,\n\t\tTOmit | OptionType | 'min' | 'max' | 'enum' | 'int'\n\t>;\n\tpublic boolean(\n\t\tname?: string,\n\t) {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, type: 'boolean', name: name }) as any;\n\t}\n\n\tpublic positional<TName extends string>(displayName: TName): Omit<\n\t\tOptionBuilderBase<\n\t\t\tBuilderConfig<'positional'>,\n\t\t\tstring | undefined,\n\t\t\tTOmit | OptionType | 'min' | 'max' | 'int' | 'alias'\n\t\t>,\n\t\tTOmit | OptionType | 'min' | 'max' | 'int' | 'alias'\n\t>;\n\tpublic positional(): Omit<\n\t\tOptionBuilderBase<\n\t\t\tBuilderConfig<'positional'>,\n\t\t\tstring | undefined,\n\t\t\tTOmit | OptionType | 'min' | 'max' | 'int' | 'alias'\n\t\t>,\n\t\tTOmit | OptionType | 'min' | 'max' | 'int' | 'alias'\n\t>;\n\tpublic positional(displayName?: string) {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, type: 'positional', name: displayName }) as any;\n\t}\n\n\tpublic alias(\n\t\t...aliases: string[]\n\t): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tTOutput,\n\t\t\tTOmit | 'alias'\n\t\t>,\n\t\tTOmit | 'alias'\n\t> {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, aliases }) as any;\n\t}\n\n\tpublic desc<TDescription extends string>(description: TDescription): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tTOutput,\n\t\t\tTOmit | 'desc'\n\t\t>,\n\t\tTOmit | 'desc'\n\t> {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, description }) as any;\n\t}\n\n\tpublic hidden(): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tTOutput,\n\t\t\tTOmit | 'hidden'\n\t\t>,\n\t\tTOmit | 'hidden'\n\t> {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, isHidden: true }) as any;\n\t}\n\n\tpublic required(): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tExclude<TOutput, undefined>,\n\t\t\tTOmit | 'required' | 'default'\n\t\t>,\n\t\tTOmit | 'required' | 'default'\n\t> {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, isRequired: true }) as any;\n\t}\n\n\tpublic default<TDefVal extends TEnums extends undefined ? Exclude<TOutput, undefined> : TEnums>(value: TDefVal): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tExclude<TOutput, undefined>,\n\t\t\tTOmit | 'enum' | 'required' | 'default',\n\t\t\tTEnums\n\t\t>,\n\t\tTOmit | 'enum' | 'required' | 'default'\n\t> {\n\t\tconst config = this.config();\n\n\t\tconst enums = config.enumVals;\n\t\tif (enums && !enums.find((v) => value === v)) {\n\t\t\tthrow new Error(\n\t\t\t\t`Option enums [ ${enums.join(', ')} ] are incompatible with default value ${value}`,\n\t\t\t);\n\t\t}\n\n\t\treturn new OptionBuilderBase({ ...config, default: value }) as any;\n\t}\n\n\tpublic enum<TValues extends [string, ...string[]], TUnion extends TValues[number] = TValues[number]>(\n\t\t...values: TValues\n\t): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tTUnion | (TOutput extends undefined ? undefined : never),\n\t\t\tTOmit | 'enum',\n\t\t\tTUnion\n\t\t>,\n\t\tTOmit | 'enum'\n\t> {\n\t\tconst config = this.config();\n\n\t\tconst defaultVal = config.default;\n\t\tif (defaultVal !== undefined && !values.find((v) => defaultVal === v)) {\n\t\t\tthrow new Error(\n\t\t\t\t`Option enums [ ${values.join(', ')} ] are incompatible with default value ${defaultVal}`,\n\t\t\t);\n\t\t}\n\n\t\treturn new OptionBuilderBase({ ...config, enumVals: values }) as any;\n\t}\n\n\tpublic min(value: number): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tTOutput,\n\t\t\tTOmit | 'min'\n\t\t>,\n\t\tTOmit | 'min'\n\t> {\n\t\tconst config = this.config();\n\n\t\tconst maxVal = config.maxVal;\n\t\tif (maxVal !== undefined && maxVal < value) {\n\t\t\tthrow new BroCliError(\"Unable to define option's min value to be higher than max value!\");\n\t\t}\n\n\t\treturn new OptionBuilderBase({ ...config, minVal: value }) as any;\n\t}\n\n\tpublic max(value: number): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tTOutput,\n\t\t\tTOmit | 'max'\n\t\t>,\n\t\tTOmit | 'max'\n\t> {\n\t\tconst config = this.config();\n\n\t\tconst minVal = config.minVal;\n\t\tif (minVal !== undefined && minVal > value) {\n\t\t\tthrow new BroCliError(\"Unable to define option's max value to be lower than min value!\");\n\t\t}\n\n\t\treturn new OptionBuilderBase({ ...config, maxVal: value }) as any;\n\t}\n\n\tpublic int(): Omit<\n\t\tOptionBuilderBase<\n\t\t\tTBuilderConfig,\n\t\t\tTOutput,\n\t\t\tTOmit | 'int'\n\t\t>,\n\t\tTOmit | 'int'\n\t> {\n\t\tconst config = this.config();\n\n\t\treturn new OptionBuilderBase({ ...config, isInt: true }) as any;\n\t}\n}\n\nexport type GenericBuilderInternalsFields = {\n\t/**\n\t * Type-level only field\n\t *\n\t * Do not attempt to access at a runtime\n\t */\n\t$output: OutputType;\n\tconfig: BuilderConfig;\n};\n\nexport type GenericBuilderInternals = {\n\t_: GenericBuilderInternalsFields;\n};\n\nexport type GenericBuilderInternalsFieldsLimited = {\n\t/**\n\t * Type-level only field\n\t *\n\t * Do not attempt to access at a runtime\n\t */\n\t$output: OutputType;\n\tconfig: BuilderConfigLimited;\n};\n\nexport type GenericBuilderInternalsLimited = {\n\t_: GenericBuilderInternalsFieldsLimited;\n};\n\nexport type ProcessedOptions<\n\tTOptionConfig extends Record<string, GenericBuilderInternals> = Record<string, GenericBuilderInternals>,\n> = {\n\t[K in keyof TOptionConfig]: K extends string ? {\n\t\t\tconfig: ProcessedBuilderConfig;\n\t\t\t/**\n\t\t\t * Type-level only field\n\t\t\t *\n\t\t\t * Do not attempt to access at a runtime\n\t\t\t */\n\t\t\t$output: TOptionConfig[K]['_']['$output'];\n\t\t}\n\t\t: never;\n};\n\nexport type Simplify<T> =\n\t& {\n\t\t[K in keyof T]: T[K];\n\t}\n\t& {};\n\nexport type TypeOf<TOptions extends Record<string, GenericBuilderInternals>> = Simplify<\n\t{\n\t\t[K in keyof TOptions]: TOptions[K]['_']['$output'];\n\t}\n>;\n\nexport function string<TName extends string>(\n\tname: TName,\n): Omit<\n\tOptionBuilderBase<\n\t\tBuilderConfig<'string'>,\n\t\tstring | undefined,\n\t\tOptionType | 'min' | 'max' | 'int'\n\t>,\n\tOptionType | 'min' | 'max' | 'int'\n>;\nexport function string(): Omit<\n\tOptionBuilderBase<\n\t\tBuilderConfig<'string'>,\n\t\tstring | undefined,\n\t\tOptionType | 'min' | 'max' | 'int'\n\t>,\n\tOptionType | 'min' | 'max' | 'int'\n>;\nexport function string<TName extends string>(name?: TName) {\n\treturn typeof name === 'string' ? new OptionBuilderBase().string(name) : new OptionBuilderBase().string();\n}\n\nexport function number<TName extends string>(\n\tname: TName,\n): Omit<\n\tOptionBuilderBase<\n\t\tBuilderConfig<'number'>,\n\t\tnumber | undefined,\n\t\tOptionType | 'enum'\n\t>,\n\tOptionType | 'enum'\n>;\nexport function number(): Omit<\n\tOptionBuilderBase<\n\t\tBuilderConfig<'number'>,\n\t\tnumber | undefined,\n\t\tOptionType | 'enum'\n\t>,\n\tOptionType | 'enum'\n>;\nexport function number<TName extends string>(name?: TName) {\n\treturn typeof name === 'string' ? new OptionBuilderBase().number(name) : new OptionBuilderBase().number();\n}\n\nexport function boolean<TName extends string>(\n\tname: TName,\n): Omit<\n\tOptionBuilderBase<\n\t\tBuilderConfig<'boolean'>,\n\t\tboolean | undefined,\n\t\tOptionType | 'min' | 'max' | 'int' | 'enum'\n\t>,\n\tOptionType | 'min' | 'max' | 'int' | 'enum'\n>;\nexport function boolean(): Omit<\n\tOptionBuilderBase<\n\t\tBuilderConfig<'boolean'>,\n\t\tboolean | undefined,\n\t\tOptionType | 'min' | 'max' | 'int' | 'enum'\n\t>,\n\tOptionType | 'min' | 'max' | 'int' | 'enum'\n>;\nexport function boolean<TName extends string>(name?: TName) {\n\treturn typeof name === 'string' ? new OptionBuilderBase().boolean(name) : new OptionBuilderBase().boolean();\n}\n\nexport function positional<TName extends string>(displayName: TName): Omit<\n\tOptionBuilderBase<\n\t\tBuilderConfig<'positional'>,\n\t\tstring | undefined,\n\t\tOptionType | 'min' | 'max' | 'int' | 'alias'\n\t>,\n\tOptionType | 'min' | 'max' | 'int' | 'alias'\n>;\nexport function positional(): Omit<\n\tOptionBuilderBase<\n\t\tBuilderConfig<'positional'>,\n\t\tstring | undefined,\n\t\tOptionType | 'min' | 'max' | 'int' | 'alias'\n\t>,\n\tOptionType | 'min' | 'max' | 'int' | 'alias'\n>;\nexport function positional(displayName?: string) {\n\treturn typeof displayName === 'string'\n\t\t? new OptionBuilderBase().positional(displayName)\n\t\t: new OptionBuilderBase().positional();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,QAAIA,SAAS,WAAW;AACxB;AAEA,eAAS,YAAY,KAAK,MAAM;AAC9B,eAAO,QAAQ,QAAQ,eAAe;AAAA,MACxC;AAEA,UAAI;AACJ,UAAI;AACF,oBAAY;AAAA,MACd,SAAQ,GAAG;AAGT,oBAAY,WAAW;AAAA,QAAC;AAAA,MAC1B;AAEA,UAAI;AACJ,UAAI;AACF,oBAAY;AAAA,MACd,SAAQ,GAAG;AACT,oBAAY,WAAW;AAAA,QAAC;AAAA,MAC1B;AAEA,UAAI;AACJ,UAAI;AACF,wBAAgB;AAAA,MAClB,SAAQ,GAAG;AACT,wBAAgB,WAAW;AAAA,QAAC;AAAA,MAC9B;AAuBA,eAASA,OAAM,QAAQ,UAAU,OAAO,WAAW,sBAAsB;AACvE,YAAI,OAAO,aAAa,UAAU;AAChC,kBAAQ,SAAS;AACjB,sBAAY,SAAS;AACrB,iCAAuB,SAAS;AAChC,qBAAW,SAAS;AAAA,QACtB;AAGA,YAAI,aAAa,CAAC;AAClB,YAAI,cAAc,CAAC;AAEnB,YAAI,YAAY,OAAO,UAAU;AAEjC,YAAI,OAAO,YAAY;AACrB,qBAAW;AAEb,YAAI,OAAO,SAAS;AAClB,kBAAQ;AAGV,iBAAS,OAAOC,SAAQC,QAAO;AAE7B,cAAID,YAAW;AACb,mBAAO;AAET,cAAIC,WAAU;AACZ,mBAAOD;AAET,cAAI;AACJ,cAAI;AACJ,cAAI,OAAOA,WAAU,UAAU;AAC7B,mBAAOA;AAAA,UACT;AAEA,cAAI,YAAYA,SAAQ,SAAS,GAAG;AAClC,oBAAQ,IAAI,UAAU;AAAA,UACxB,WAAW,YAAYA,SAAQ,SAAS,GAAG;AACzC,oBAAQ,IAAI,UAAU;AAAA,UACxB,WAAW,YAAYA,SAAQ,aAAa,GAAG;AAC7C,oBAAQ,IAAI,cAAc,SAAU,SAAS,QAAQ;AACnD,cAAAA,QAAO,KAAK,SAAS,OAAO;AAC1B,wBAAQ,OAAO,OAAOC,SAAQ,CAAC,CAAC;AAAA,cAClC,GAAG,SAAS,KAAK;AACf,uBAAO,OAAO,KAAKA,SAAQ,CAAC,CAAC;AAAA,cAC/B,CAAC;AAAA,YACH,CAAC;AAAA,UACH,WAAWF,OAAM,UAAUC,OAAM,GAAG;AAClC,oBAAQ,CAAC;AAAA,UACX,WAAWD,OAAM,WAAWC,OAAM,GAAG;AACnC,oBAAQ,IAAI,OAAOA,QAAO,QAAQ,iBAAiBA,OAAM,CAAC;AAC1D,gBAAIA,QAAO,UAAW,OAAM,YAAYA,QAAO;AAAA,UACjD,WAAWD,OAAM,SAASC,OAAM,GAAG;AACjC,oBAAQ,IAAI,KAAKA,QAAO,QAAQ,CAAC;AAAA,UACnC,WAAW,aAAa,OAAO,SAASA,OAAM,GAAG;AAC/C,gBAAI,OAAO,aAAa;AAEtB,sBAAQ,OAAO,YAAYA,QAAO,MAAM;AAAA,YAC1C,OAAO;AAEL,sBAAQ,IAAI,OAAOA,QAAO,MAAM;AAAA,YAClC;AACA,YAAAA,QAAO,KAAK,KAAK;AACjB,mBAAO;AAAA,UACT,WAAW,YAAYA,SAAQ,KAAK,GAAG;AACrC,oBAAQ,OAAO,OAAOA,OAAM;AAAA,UAC9B,OAAO;AACL,gBAAI,OAAO,aAAa,aAAa;AACnC,sBAAQ,OAAO,eAAeA,OAAM;AACpC,sBAAQ,OAAO,OAAO,KAAK;AAAA,YAC7B,OACK;AACH,sBAAQ,OAAO,OAAO,SAAS;AAC/B,sBAAQ;AAAA,YACV;AAAA,UACF;AAEA,cAAI,UAAU;AACZ,gBAAI,QAAQ,WAAW,QAAQA,OAAM;AAErC,gBAAI,SAAS,IAAI;AACf,qBAAO,YAAY,KAAK;AAAA,YAC1B;AACA,uBAAW,KAAKA,OAAM;AACtB,wBAAY,KAAK,KAAK;AAAA,UACxB;AAEA,cAAI,YAAYA,SAAQ,SAAS,GAAG;AAClC,YAAAA,QAAO,QAAQ,SAAS,OAAO,KAAK;AAClC,kBAAI,WAAW,OAAO,KAAKC,SAAQ,CAAC;AACpC,kBAAI,aAAa,OAAO,OAAOA,SAAQ,CAAC;AACxC,oBAAM,IAAI,UAAU,UAAU;AAAA,YAChC,CAAC;AAAA,UACH;AACA,cAAI,YAAYD,SAAQ,SAAS,GAAG;AAClC,YAAAA,QAAO,QAAQ,SAAS,OAAO;AAC7B,kBAAI,aAAa,OAAO,OAAOC,SAAQ,CAAC;AACxC,oBAAM,IAAI,UAAU;AAAA,YACtB,CAAC;AAAA,UACH;AAEA,mBAAS,KAAKD,SAAQ;AACpB,gBAAI;AACJ,gBAAI,OAAO;AACT,sBAAQ,OAAO,yBAAyB,OAAO,CAAC;AAAA,YAClD;AAEA,gBAAI,SAAS,MAAM,OAAO,MAAM;AAC9B;AAAA,YACF;AACA,kBAAM,CAAC,IAAI,OAAOA,QAAO,CAAC,GAAGC,SAAQ,CAAC;AAAA,UACxC;AAEA,cAAI,OAAO,uBAAuB;AAChC,gBAAI,UAAU,OAAO,sBAAsBD,OAAM;AACjD,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAGvC,kBAAI,SAAS,QAAQ,CAAC;AACtB,kBAAI,aAAa,OAAO,yBAAyBA,SAAQ,MAAM;AAC/D,kBAAI,cAAc,CAAC,WAAW,cAAc,CAAC,sBAAsB;AACjE;AAAA,cACF;AACA,oBAAM,MAAM,IAAI,OAAOA,QAAO,MAAM,GAAGC,SAAQ,CAAC;AAChD,kBAAI,CAAC,WAAW,YAAY;AAC1B,uBAAO,eAAe,OAAO,QAAQ;AAAA,kBACnC,YAAY;AAAA,gBACd,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAEA,cAAI,sBAAsB;AACxB,gBAAI,mBAAmB,OAAO,oBAAoBD,OAAM;AACxD,qBAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,kBAAI,eAAe,iBAAiB,CAAC;AACrC,kBAAI,aAAa,OAAO,yBAAyBA,SAAQ,YAAY;AACrE,kBAAI,cAAc,WAAW,YAAY;AACvC;AAAA,cACF;AACA,oBAAM,YAAY,IAAI,OAAOA,QAAO,YAAY,GAAGC,SAAQ,CAAC;AAC5D,qBAAO,eAAe,OAAO,cAAc;AAAA,gBACzC,YAAY;AAAA,cACd,CAAC;AAAA,YACH;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,eAAO,OAAO,QAAQ,KAAK;AAAA,MAC7B;AASA,MAAAF,OAAM,iBAAiB,SAAS,eAAe,QAAQ;AACrD,YAAI,WAAW;AACb,iBAAO;AAET,YAAI,IAAI,WAAY;AAAA,QAAC;AACrB,UAAE,YAAY;AACd,eAAO,IAAI,EAAE;AAAA,MACf;AAIA,eAAS,WAAW,GAAG;AACrB,eAAO,OAAO,UAAU,SAAS,KAAK,CAAC;AAAA,MACzC;AACA,MAAAA,OAAM,aAAa;AAEnB,eAAS,SAAS,GAAG;AACnB,eAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,MACpD;AACA,MAAAA,OAAM,WAAW;AAEjB,eAAS,UAAU,GAAG;AACpB,eAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,MACpD;AACA,MAAAA,OAAM,YAAY;AAElB,eAAS,WAAW,GAAG;AACrB,eAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,MACpD;AACA,MAAAA,OAAM,aAAa;AAEnB,eAAS,iBAAiB,IAAI;AAC5B,YAAI,QAAQ;AACZ,YAAI,GAAG,OAAQ,UAAS;AACxB,YAAI,GAAG,WAAY,UAAS;AAC5B,YAAI,GAAG,UAAW,UAAS;AAC3B,eAAO;AAAA,MACT;AACA,MAAAA,OAAM,mBAAmB;AAEzB,aAAOA;AAAA,IACP,EAAG;AAEH,QAAI,OAAO,WAAW,YAAY,OAAO,SAAS;AAChD,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AChQA;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,MAAM,IAAI;AACnC,aAAO,GAAG,IAAI,SAAU,GAAG;AAC1B,YAAI,KAAK,OAAO,MAAM,UAAU;AAC/B,iBAAO,EAAE,GAAG,QAAQ,QAAQ,MAAM;AAAA,QACnC;AACA,YAAK,QAAS,KAAK,CAAC,KAAK,CAAE,IAAK,KAAK,CAAC,GAAG;AACxC,iBAAO,MAAM,EAAE,QAAQ,YAAY,MAAM,IAAI;AAAA,QAC9C;AACA,YAAK,SAAU,KAAK,CAAC,GAAG;AACvB,iBAAO,MAAM,EAAE,QAAQ,eAAe,MAAM,IAAI;AAAA,QACjD;AACA,eAAO,OAAO,CAAC,EAAE,QAAQ,gDAAgD,QAAQ;AAAA,MAClF,CAAC,EAAE,KAAK,GAAG;AAAA,IACZ;AAAA;AAAA;;;ACfA;AAAA;AAAA;AAIA,QAAI,UAAU,QAAQ;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,EAAE,KAAK,GAAG,IAAI;AACd,QAAI,YAAY,IAAI,OAAO,MAAM,UAAU,GAAG;AAC9C,QAAI,OAAO;AACX,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAI,OAAO;AAEX,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AAET,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,SAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,gBAAU,OAAO,KAAK,OAAO,GAAG,SAAS,EAAE;AAAA,IAC5C;AAFS;AAGT,QAAI,kBAAkB,IAAI,OAAO,MAAM,KAAK;AAE5C,aAAS,SAAS,GAAG,GAAG;AACvB,UAAI,YAAY,EAAE;AAElB,UAAI,UAAU,CAAC;AACf,UAAI;AAEJ,aAAQ,WAAW,EAAE,KAAK,CAAC,GAAI;AAC9B,gBAAQ,KAAK,QAAQ;AACrB,YAAI,EAAE,cAAc,SAAS,OAAO;AACnC,YAAE,aAAa;AAAA,QAChB;AAAA,MACD;AAEA,QAAE,YAAY;AAEd,aAAO;AAAA,IACR;AAEA,aAAS,OAAO,KAAK,KAAK,KAAK;AAC9B,UAAI,IAAI,OAAO,QAAQ,aAAa,IAAI,GAAG,IAAI,IAAI,GAAG;AACtD,UAAI,OAAO,MAAM,eAAe,OAAO,IAAI;AAC1C,YAAI;AAAA,MACL,WAAW,OAAO,MAAM,aAAa;AACpC,YAAI;AAAA,MACL;AAEA,UAAI,OAAO,MAAM,UAAU;AAC1B,eAAO,MAAM,QAAQ,KAAK,UAAU,CAAC,IAAI;AAAA,MAC1C;AACA,aAAO,MAAM;AAAA,IACd;AAEA,aAAS,cAAcG,SAAQ,KAAK,MAAM;AACzC,UAAI,CAAC,MAAM;AACV,eAAO,CAAC;AAAA,MACT;AACA,UAAI,KAAK,KAAK,UAAU;AACxB,UAAI,WAAW,QAAQ,KAAK,QAAS,OAAO,cAAe,OAAO;AAElE,UAAI,UAAU,IAAI,OAAO;AAAA,QACxB,MAAM,UAAU;AAAA;AAAA,QAChB,MAAM,WAAW,MAAM,eAAe,MAAM,eAAe;AAAA,MAC5D,EAAE,KAAK,GAAG,GAAG,GAAG;AAEhB,UAAI,UAAU,SAASA,SAAQ,OAAO;AAEtC,UAAI,QAAQ,WAAW,GAAG;AACzB,eAAO,CAAC;AAAA,MACT;AACA,UAAI,CAAC,KAAK;AACT,cAAM,CAAC;AAAA,MACR;AAEA,UAAI,YAAY;AAEhB,aAAO,QAAQ,IAAI,SAAU,OAAO;AACnC,YAAI,IAAI,MAAM,CAAC;AACf,YAAI,CAAC,KAAK,WAAW;AACpB,iBAAO;AAAA,QACR;AACA,YAAI,UAAU,KAAK,CAAC,GAAG;AACtB,iBAAO,EAAE,IAAI,EAAE;AAAA,QAChB;AAaA,YAAI,QAAQ;AACZ,YAAI,MAAM;AACV,YAAI,MAAM;AACV,YAAI,SAAS;AACb,YAAIC;AAEJ,iBAAS,cAAc;AACtB,UAAAA,MAAK;AACL,cAAI;AACJ,cAAI;AACJ,cAAI,OAAO,EAAE,OAAOA,EAAC;AAErB,cAAI,SAAS,KAAK;AACjB,YAAAA,MAAK;AACL,gBAAI,EAAE,OAAOA,EAAC,MAAM,KAAK;AACxB,oBAAM,IAAI,MAAM,uBAAuB,EAAE,MAAMA,KAAI,GAAGA,KAAI,CAAC,CAAC;AAAA,YAC7D;AACA,qBAAS,EAAE,QAAQ,KAAKA,EAAC;AACzB,gBAAI,SAAS,GAAG;AACf,oBAAM,IAAI,MAAM,uBAAuB,EAAE,MAAMA,EAAC,CAAC;AAAA,YAClD;AACA,sBAAU,EAAE,MAAMA,IAAG,MAAM;AAC3B,YAAAA,KAAI;AAAA,UACL,WAAY,aAAc,KAAK,IAAI,GAAG;AACrC,sBAAU;AACV,YAAAA,MAAK;AAAA,UACN,OAAO;AACN,gBAAI,cAAc,EAAE,MAAMA,EAAC;AAC3B,qBAAS,YAAY,MAAM,UAAU;AACrC,gBAAI,CAAC,QAAQ;AACZ,wBAAU;AACV,cAAAA,KAAI,EAAE;AAAA,YACP,OAAO;AACN,wBAAU,YAAY,MAAM,GAAG,OAAO,KAAK;AAC3C,cAAAA,MAAK,OAAO,QAAQ;AAAA,YACrB;AAAA,UACD;AACA,iBAAO,OAAO,KAAK,IAAI,OAAO;AAAA,QAC/B;AAEA,aAAKA,KAAI,GAAGA,KAAI,EAAE,QAAQA,MAAK;AAC9B,cAAI,IAAI,EAAE,OAAOA,EAAC;AAClB,mBAAS,UAAW,CAAC,UAAU,MAAM,OAAO,MAAM;AAClD,cAAI,KAAK;AACR,mBAAO;AACP,kBAAM;AAAA,UACP,WAAW,OAAO;AACjB,gBAAI,MAAM,OAAO;AAChB,sBAAQ;AAAA,YACT,WAAW,SAAS,IAAI;AACvB,qBAAO;AAAA,YACR,OAAO;AACN,kBAAI,MAAM,IAAI;AACb,gBAAAA,MAAK;AACL,oBAAI,EAAE,OAAOA,EAAC;AACd,oBAAI,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACrC,yBAAO;AAAA,gBACR,OAAO;AACN,yBAAO,KAAK;AAAA,gBACb;AAAA,cACD,WAAW,MAAM,IAAI;AACpB,uBAAO,YAAY;AAAA,cACpB,OAAO;AACN,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD,WAAW,MAAM,MAAM,MAAM,IAAI;AAChC,oBAAQ;AAAA,UACT,WAAW,UAAU,KAAK,CAAC,GAAG;AAC7B,mBAAO,EAAE,IAAI,EAAE;AAAA,UAChB,WAAW,KAAK,KAAK,CAAC,GAAG;AACxB,wBAAY;AACZ,gBAAI,aAAa,EAAE,SAASD,QAAO,MAAM,MAAM,QAAQC,KAAI,CAAC,EAAE;AAC9D,gBAAI,IAAI,QAAQ;AACf,qBAAO,CAAC,KAAK,UAAU;AAAA,YACxB;AACA,mBAAO,CAAC,UAAU;AAAA,UACnB,WAAW,MAAM,IAAI;AACpB,kBAAM;AAAA,UACP,WAAW,MAAM,IAAI;AACpB,mBAAO,YAAY;AAAA,UACpB,OAAO;AACN,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,YAAI,QAAQ;AACX,iBAAO,EAAE,IAAI,QAAQ,SAAS,IAAI;AAAA,QACnC;AAEA,eAAO;AAAA,MACR,CAAC,EAAE,OAAO,SAAU,MAAM,KAAK;AAE9B,eAAO,OAAO,QAAQ,cAAc,OAAO,KAAK,OAAO,GAAG;AAAA,MAC3D,GAAG,CAAC,CAAC;AAAA,IACN;AAEA,WAAO,UAAU,SAAS,MAAM,GAAG,KAAK,MAAM;AAC7C,UAAI,SAAS,cAAc,GAAG,KAAK,IAAI;AACvC,UAAI,OAAO,QAAQ,YAAY;AAC9B,eAAO;AAAA,MACR;AACA,aAAO,OAAO,OAAO,SAAU,KAAKC,IAAG;AACtC,YAAI,OAAOA,OAAM,UAAU;AAC1B,iBAAO,IAAI,OAAOA,EAAC;AAAA,QACpB;AACA,YAAI,KAAKA,GAAE,MAAM,OAAO,MAAM,QAAQ,QAAQ,QAAQ,KAAK,GAAG,CAAC;AAC/D,YAAI,GAAG,WAAW,GAAG;AACpB,iBAAO,IAAI,OAAO,GAAG,CAAC,CAAC;AAAA,QACxB;AACA,eAAO,IAAI,OAAO,GAAG,OAAO,OAAO,EAAE,IAAI,SAAU,GAAG;AACrD,cAAI,gBAAgB,KAAK,CAAC,GAAG;AAC5B,mBAAO,KAAK,MAAM,EAAE,MAAM,KAAK,EAAE,CAAC,CAAC;AAAA,UACpC;AACA,iBAAO;AAAA,QACR,CAAC,CAAC;AAAA,MACH,GAAG,CAAC,CAAC;AAAA,IACN;AAAA;AAAA;;;ACjOA;AAAA;AAAA;AAEA,YAAQ,QAAQ;AAChB,YAAQ,QAAQ;AAAA;AAAA;;;ACIT,IAAM,cAAN,cAA0B,MAAM;AAAA,EACtC,YAAY,SAAoC,OAAqB;AACpE,UAAM,YAAY;AAClB,UAAM,YAAY,SAAY,UAAU,GAAG,SAAS,GAAG,OAAO,EAAE;AAFjB;AAAA,EAGhD;AACD;;;ACZA,mBAAkB;;;AC2GlB,IAAM,oBAAoB,CAAC,WAA0B;AACpD,MAAI,SAAS;AAEb,UAAQ,OAAO,MAAM;AAAA,IACpB,KAAK;AACJ,eAAS;AACT;AAAA,IACD,KAAK,UAAU;AACd,WAAK,OAAO,UAAU,OAAO,YAAY,QAAW;AACnD,YAAI,OAAO;AAEX,YAAI,OAAO,MAAO,QAAO,OAAO;AAEhC,YAAI,OAAO,WAAW,OAAW,QAAO,OAAO,IAAI,OAAO,MAAM;AAAA,YAC3D,QAAO,OAAO;AAEnB,YAAI,OAAO,WAAW,OAAW,QAAO,OAAO,GAAG,OAAO,MAAM;AAAA,YAC1D,QAAO,OAAO;AAEnB,iBAAS;AACT;AAAA,MACD;AAEA,UAAI,OAAO,OAAO;AACjB,iBAAS;AACT;AAAA,MACD;AAEA,eAAS;AACT;AAAA,IACD;AAAA,IACA,KAAK,UAAU;AACd,UAAI,OAAO,UAAU;AACpB,iBAAS,OAAO,OAAO,SAAS,KAAK,KAAK,IAAI;AAC9C;AAAA,MACD;AAEA,eAAS;AACT;AAAA,IACD;AAAA,IACA,KAAK,cAAc;AAClB,eAAS,GAAG,OAAO,aAAa,MAAM,GAAG,GAAG,OAAO,WAAW,OAAO,SAAS,KAAK,GAAG,IAAI,OAAO,IAAI,GACpG,OAAO,aAAa,MAAM,GAC3B;AACA;AAAA,IACD;AAAA,EACD;AAEA,MAAI,OAAO,cAAc,OAAO,SAAS,aAAc,UAAS,MAAM,OAAO,SAAS,MAAM,KAAK;AACjG,SAAO;AACR;AAQO,IAAM,sBAAoC,OAAO,UAAU;AACjE,UAAQ,MAAM,MAAM;AAAA,IACnB,KAAK,gBAAgB;AACpB,YAAMC,WAAU,MAAM;AACtB,YAAM,cAAc,0BAA0BA,QAAO;AACrD,YAAM,UAAU,MAAM;AACtB,YAAM,OAAOA,SAAQ,QAAQA,SAAQ;AACrC,YAAM,OAAOA,SAAQ,aAAa,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM;AACzD,YAAM,cAAc,QAAQ,KAAK,SAAS,OAAO;AAEjD,UAAI,SAAS,QAAW;AACvB,gBAAQ,IAAI;AAAA,EAAK,IAAI,EAAE;AAAA,MACxB;AAEA,YAAM,OAAO,OAAO,OAAOA,SAAQ,WAAW,CAAC,CAA+C,EAAE;AAAA,QAAO,CAAC,QACvG,CAAC,IAAI,OAAO;AAAA,MACb;AACA,YAAM,cAAc,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,SAAS,YAAY;AACzE,YAAM,UAAU,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,SAAS,YAAY;AAErE,cAAQ,IAAI,UAAU;AACtB,UAAIA,SAAQ,SAAS;AACpB,gBAAQ;AAAA,UACP,KAAK,UAAU,UAAU,MAAM,EAAE,GAAG,WAAW,GAC9C,YAAY,SACT,MACC,YAAY,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC,CAAC,EAAE,KAAK,GAAG,IAClE,EACJ;AAAA,QACD;AAAA,MACD,MAAO,SAAQ,IAAI,KAAK,UAAU,UAAU,MAAM,EAAE,GAAG,WAAW,YAAY;AAE9E,UAAIA,SAAQ,SAAS;AACpB,gBAAQ,IAAI;AAAA,SAAY;AACxB,gBAAQ,IAAI,KAAK,CAACA,SAAQ,MAAM,GAAGA,SAAQ,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE;AAAA,MACjE;AAEA,UAAI,aAAa;AAChB,gBAAQ,IAAI,uBAAuB;AACnC,cAAM,UAAU;AAChB,cAAM,YAAY,YAAY,OAAO,CAAC,GAAG,MAAM,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,SAAS,GAAG,CAAC;AACvF,cAAM,eAAe,YAAY;AACjC,cAAM,aAAa,IAAI;AAEvB,cAAM,OAAO,YAAY;AAAA,UAAI,CAAC,MAC7B,KAAK,EAAE,KAAK,OAAO,YAAY,CAAC,IAC9B,MAAM;AACN,kBAAM,cAAc,EAAE,aAAa,EAAE;AACrC,gBAAI,CAAC,aAAa,OAAQ,QAAO;AACjC,kBAAM,QAAQ,YAAY,MAAM,IAAI;AACpC,kBAAM,QAAQ,MAAM,MAAM;AAE1B,kBAAM,QAAQ,CAAC,OAAO,GAAG,MAAM,IAAI,CAACC,OAAM,GAAG,OAAO,UAAU,IAAIA,EAAC,CAAC,EAAE,KAAK,IAAI;AAE/E,mBAAO;AAAA,UACR,GAAG,CACJ;AAAA,QACD,EACE,KAAK,IAAI;AACX,gBAAQ,IAAI,IAAI;AAAA,MACjB;AAEA,UAAI,QAAQ,QAAQ;AACnB,cAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,MAAM;AAC5C,gBAAM,gBAAgB,EAAE,OAAO,QAAQ,OAAO,CAAC,IAAI,MAAM,KAAK,EAAE,QAAQ,CAAC,KACpE,EAAE,OAAO,QAAQ,SAAS,KAAK,IAAK;AAEzC,iBAAO,gBAAgB,IAAI,gBAAgB;AAAA,QAC5C,GAAG,CAAC;AACJ,cAAM,oBAAoB,cAAc,IAAI,cAAc,IAAI;AAC9D,cAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,MAAM;AAC3C,gBAAM,UAAU,kBAAkB,EAAE,MAAM,EAAE;AAC5C,gBAAM,SAAS,UAAU,IAAI,EAAE,OAAO,KAAK,SAAS,IAAI,UAAU,EAAE,OAAO,KAAK;AAEhF,iBAAO,SAAS,IAAI,SAAS;AAAA,QAC9B,GAAG,CAAC,IAAI;AAER,cAAM,aAAa,oBAAoB,aAAa;AAEpD,cAAM,OAAO,QAAQ;AAAA,UAAI,CAAC,EAAE,QAAQ,IAAI,MACvC,KAAK,GAAG,IAAI,QAAQ,SAAS,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM,EAAE,GAAG,OAAO,iBAAiB,CAAC,GACzF,GAAG,IAAI,IAAI,IACT,MAAM;AACN,kBAAM,WAAW,kBAAkB,GAAG;AACtC,mBAAO,SAAS,SAAS,MAAM,WAAW;AAAA,UAC3C,GAAG,CACJ,GAAG,OAAO,UAAU,CACrB,IACE,MAAM;AACN,gBAAI,CAAC,IAAI,aAAa,QAAQ;AAC7B,qBAAO,IAAI,YAAY,SACpB,YAAY,KAAK,UAAU,IAAI,OAAO,CAAC,KACvC;AAAA,YACJ;AAEA,kBAAM,QAAQ,IAAI,YAAY,MAAM,IAAI;AACxC,kBAAM,QAAQ,MAAM,MAAM;AAC1B,kBAAM,MAAM,IAAI,YAAY,SAAY,cAAc,KAAK,UAAU,IAAI,OAAO,CAAC,MAAM;AAEvF,kBAAM,QAAQ,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,GAAG,OAAO,UAAU,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI;AAEnF,mBAAO;AAAA,UACR,GAAG,CACJ;AAAA,QACD,EAAE,KAAK,IAAI;AAEX,gBAAQ,IAAI,UAAU;AACtB,gBAAQ,IAAI,IAAI;AAAA,MACjB;AAEA,cAAQ,IAAI,iBAAiB;AAC7B,cAAQ,IAAI,8BAA8B,WAAW,EAAE;AACvD,cAAQ,IAAI,4BAA4B,UAAU,QAAQ,OAAO,KAAK,EAAE,EAAE;AAE1E,UAAI,aAAa;AAChB,gBAAQ;AAAA,UACP;AAAA,OACC,UAAU,UAAU,MAAM,EAC3B,GAAG,WAAW;AAAA;AAAA,QACf;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,IAEA,KAAK,eAAe;AACnB,YAAM,UAAU,MAAM;AACtB,YAAM,OAAO,MAAM;AACnB,YAAM,WAAW,MAAM,SAAS,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM;AAEvD,UAAI,SAAS,QAAW;AACvB,gBAAQ,IAAI,GAAG,IAAI;AAAA,CAAI;AAAA,MACxB;AAEA,cAAQ,IAAI,QAAQ;AACpB,cAAQ,IAAI,KAAK,UAAU,UAAU,MAAM,EAAE,WAAW;AAExD,UAAI,SAAS,QAAQ;AACpB,gBAAQ,IAAI,uBAAuB;AACnC,cAAM,UAAU;AAChB,cAAM,YAAY,SAAS,OAAO,CAAC,GAAG,MAAM,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,SAAS,GAAG,CAAC;AACpF,cAAM,eAAe,YAAY;AAEjC,cAAM,OAAO,SAAS;AAAA,UAAI,CAAC,MAC1B,KAAK,EAAE,KAAK,OAAO,YAAY,CAAC,IAC9B,MAAM;AACN,kBAAMC,QAAO,EAAE,aAAa,EAAE;AAE9B,gBAAI,CAACA,OAAM,OAAQ,QAAO;AAE1B,kBAAM,QAAQA,MAAK,MAAM,IAAI;AAC7B,kBAAM,QAAQ,MAAM,MAAM;AAE1B,kBAAM,QAAQ,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,GAAG,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI;AAErF,mBAAO;AAAA,UACR,GAAG,CACJ;AAAA,QACD,EACE,KAAK,IAAI;AACX,gBAAQ,IAAI,IAAI;AAAA,MACjB,OAAO;AACN,gBAAQ,IAAI,0BAA0B;AAAA,MACvC;AAEA,cAAQ,IAAI,UAAU;AACtB,cAAQ,IAAI,yBAAyB,UAAU,QAAQ,OAAO,KAAK,EAAE,EAAE;AACvE,cAAQ,IAAI,4BAA4B,UAAU,QAAQ,OAAO,KAAK,EAAE,EAAE;AAC1E,cAAQ,IAAI,IAAI;AAEhB,aAAO;AAAA,IACR;AAAA,IAEA,KAAK,WAAW;AACf,aAAO;AAAA,IACR;AAAA,IAEA,KAAK,SAAS;AACb,UAAI;AAEJ,cAAQ,MAAM,WAAW;AAAA,QACxB,KAAK,aAAa;AACjB,gBAAM,cAAc,MAAM,SAAS;AACnC,gBAAM,OAAO,MAAM,SAAS;AAC5B,gBAAM,SAAS,MAAM;AAErB,gBAAM,MAAM,OAAO;AACnB,gBACC,wCAAwC,WAAW,8BAA8B,GAAG,sBAAsB,IAAI;AAE/G;AAAA,QACD;AAAA,QAEA,KAAK,aAAa;AACjB,gBAAM,cAAc,MAAM,SAAS;AACnC,gBAAM,OAAO,MAAM,SAAS;AAC5B,gBAAM,SAAS,MAAM;AAErB,gBAAM,MAAM,OAAO;AAEnB,gBACC,wCAAwC,WAAW,8BAA8B,GAAG,sBAAsB,IAAI;AAE/G;AAAA,QACD;AAAA,QAEA,KAAK,gBAAgB;AACpB,gBAAM,cAAc,MAAM,SAAS;AACnC,gBAAM,OAAO,MAAM,SAAS;AAE5B,gBAAM,wCAAwC,WAAW,0CAA0C,IAAI;AAEvG;AAAA,QACD;AAAA,QAEA,KAAK,0BAA0B;AAC9B,gBAAM,cAAc,MAAM,SAAS;AACnC,gBAAM,OAAO,MAAM,SAAS;AAE5B,gBACC,0CAA0C,WAAW,2DAA2D,WAAW,cAAc,WAAW,cAAc,WAAW;AAAA;AAE9K;AAAA,QACD;AAAA,QAEA,KAAK,yBAAyB;AAC7B,gBAAM,cAAc,MAAM,SAAS;AAEnC,gBACC,yCAAyC,WAAW,2DAA2D,WAAW,cAAc,WAAW;AAEpJ;AAAA,QACD;AAAA,QAEA,KAAK,yBAAyB;AAC7B,gBAAM,cAAc,MAAM,SAAS;AAEnC,gBACC,yCAAyC,WAAW,2DAA2D,WAAW,cAAc,WAAW;AAEpJ;AAAA,QACD;AAAA,QAEA,KAAK,wBAAwB;AAC5B,gBAAM,cAAc,MAAM,SAAS;AACnC,gBAAM,OAAO,MAAM,SAAS;AAE5B,gBAAM,wCAAwC,WAAW,wCAAwC,IAAI;AACrG;AAAA,QACD;AAAA,QAEA,KAAK,kBAAkB;AACtB,gBAAM,cAAc,MAAM,SAAS;AACnC,gBAAM,OAAO,MAAM,SAAS;AAC5B,gBAAM,SAAS,MAAM;AAErB,gBAAM,SAAS,OAAO;AAEtB,gBAAM,OAAO,SAAS,eACnB,qDAAqD,OAAO,IAAI,0CACjE,OAAO,KAAK,IAAI,CACjB,eAAe,IAAI,KACjB,0CAA0C,WAAW,0CACtD,OAAO,KAAK,IAAI,CACjB,eAAe,IAAI;AAEpB;AAAA,QACD;AAAA,QAEA,KAAK,yBAAyB;AAC7B,gBAAMC,OAAM,qBAAqB,MAAM,QAAQ;AAAA;AAE/C,kBAAQ,MAAMA,IAAG;AAEjB,iBAAO;AAAA,QACR;AAAA,QAEA,KAAK,4BAA4B;AAChC,gBAAM,QAAQ,0BAA0B,MAAM,OAAO;AACrD,gBAAMA,OACL,oBAAoB,KAAK,IAAI,MAAM,QAAQ;AAAA,QAAY,KAAK;AAE7D,kBAAQ,MAAMA,IAAG;AAEjB,iBAAO;AAAA,QACR;AAAA,QAEA,KAAK,sBAAsB;AAC1B,gBAAM,EAAE,SAAS,aAAa,SAAAH,SAAQ,IAAI;AAE1C,gBAAM,YAAYA,SAAQ,IAAI,4CAC7B,YAAY,IAAI,CAAC,QAAQ;AACxB,kBAAM,OAAO,IAAI,MAAM;AACvB,kBAAM,UAAU;AAEhB,gBAAI,QAAQ,OAAQ,QAAO,GAAG,IAAI,KAAK,QAAQ,KAAK,IAAI,CAAC;AAEzD,mBAAO;AAAA,UACR,CAAC,EAAE,KAAK,IAAI,CACb;AAEA;AAAA,QACD;AAAA,QAEA,KAAK,2BAA2B;AAC/B,gBAAM,EAAE,SAAAA,UAAS,aAAa,IAAI;AAClC,gBAAM,qCAAqCA,SAAQ,IAAI,MAAM,aAAa,KAAK,IAAI,CAAC;AAEpF;AAAA,QACD;AAAA,QAEA,KAAK,iBAAiB;AACrB,gBAAM,IAAI,MAAM;AAChB,kBAAQ,MAAM,OAAO,MAAM,YAAY,MAAM,QAAQ,aAAa,IAAI,EAAE,UAAU,CAAC;AAEnF,iBAAO;AAAA,QACR;AAAA,MACD;AAEA,cAAQ,MAAM,GAAG;AAEjB,aAAO;AAAA,IACR;AAAA,EACD;AAGA,SAAO;AACR;AAEO,IAAM,sBAAsB,CAAC,uBAAqC,OAAO,UAC/E,MAAM,mBAAmB,KAAK,IAAI,OAAO,MAAM,oBAAoB,KAAK;;;AC/ezE,yBAAqC;AAE9B,SAAS,MAAM,OAAe;AACpC,SAAO,UAAU,KAAK,MAAM,KAAK;AAClC;AAEO,IAAM,YAAY,CAAC,YAAgB,mBAAAI,OAAY,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAE3E,IAAM,eAAe,OAAO,WAClC,OAAO,WAAW,WAAW,QAAQ,IAAI,MAAM,IAAI,SAAS,MAAM,OAAO,IAAI;;;AFkH9E,IAAM,iBAAiB,CAAC,SAAiB,KAAK,WAAW,GAAG,IAAI,OAAO,KAAK,SAAS,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;AAE/G,IAAM,kBAAkB,CACvB,WACqC;AACrC,QAAM,aAAS,aAAAC,SAAM,MAAM;AAE3B,QAAM,UAAqD,CAAC;AAE5D,QAAM,cAAuC,CAAC;AAE9C,QAAM,aAAa,OAAO,QAAQ,MAAM;AAExC,aAAW,CAAC,KAAK,KAAK,KAAK,YAAY;AACtC,UAAM,MAAM,MAAM,EAAE;AAEpB,QAAI,IAAI,SAAS,OAAW,KAAI,OAAO;AAEvC,QAAI,IAAI,SAAS,aAAc;AAE/B,QAAI,IAAI,KAAM,SAAS,GAAG,GAAG;AAC5B,YAAM,IAAI;AAAA,QACT,wBAAwB,eAAe,IAAI,IAAI,CAAC;AAAA,MACjD;AAAA,IACD;AAEA,eAAW,SAAS,IAAI,SAAS;AAChC,UAAI,MAAM,SAAS,GAAG,GAAG;AACxB,cAAM,IAAI;AAAA,UACT,wBAAwB,eAAe,IAAI,IAAI,CAAC;AAAA,QACjD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,OAAO,eAAe,IAAI,IAAI;AAElC,QAAI,UAAU,IAAI,QAAQ,IAAI,CAAC,MAAM,eAAe,CAAC,CAAC;AAAA,EACvD;AAEA,aAAW,CAAC,KAAK,KAAK,KAAK,YAAY;AACtC,UAAM,MAAM,MAAM,EAAE;AAEpB,QAAI,IAAI,SAAS,cAAc;AAC9B,cAAQ,KAAK,CAAC,KAAK,EAAE,QAAQ,KAAK,SAAS,OAAiB,CAAC,CAAC;AAE9D;AAAA,IACD;AAEA,UAAM,gBAAgB,CAAC,UAAU,MAAM,aAAa,IAAI;AAExD,UAAM,WAAW,CAAC,IAAI,MAAM,GAAG,IAAI,OAAO;AAE1C,eAAW,QAAQ,UAAU;AAC5B,YAAM,QAAQ,cAAc,KAAK,CAAC,MAAM,MAAM,IAAI;AAClD,UAAI,MAAO,OAAM,IAAI,YAAY,wBAAwB,IAAI,IAAI,aAAa,KAAK,gBAAgB;AAAA,IACpG;AAEA,eAAW,WAAW,aAAa;AAClC,YAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,MAAM,IAAI,IAAI;AAEvD,UAAI,CAAC,aAAc;AAEnB,YAAM,IAAI;AAAA,QACT,wBAAwB,IAAI,IAAI,yCAAyC,QAAQ,CAAC,CAAC;AAAA,MACpF;AAAA,IACD;AAEA,eAAW,SAAS,IAAI,SAAS;AAChC,iBAAW,WAAW,aAAa;AAClC,cAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,MAAM,KAAK;AAEpD,YAAI,CAAC,aAAc;AAEnB,cAAM,IAAI;AAAA,UACT,wBAAwB,IAAI,IAAI,cAAc,KAAK,kCAAkC,QAAQ,CAAC,CAAC;AAAA,QAChG;AAAA,MACD;AAAA,IACD;AAEA,UAAM,eAAe,CAAC,IAAI,MAAO,GAAG,IAAI,OAAO;AAE/C,gBAAY,KAAK,YAAY;AAE7B,iBAAa,QAAQ,CAAC,MAAM,QAAQ;AACnC,UAAI,aAAa,UAAU,CAAC,MAAM,MAAM,IAAI,MAAM,IAAK;AAEvD,YAAM,IAAI;AAAA,QACT,wBAAwB,IAAI,IAAI,wBAAwB,IAAI;AAAA,MAC7D;AAAA,IACD,CAAC;AAED,YAAQ,KAAK,CAAC,KAAK,EAAE,QAAQ,KAAK,SAAS,OAAiB,CAAC,CAAC;AAAA,EAC/D;AAEA,SAAO,OAAO,YAAY,OAAO;AAClC;AAEA,IAAM,eAAe,CAAC,QAAiB,gBACtC,YAAY,QAAQ,CAAC,MAAM;AAC1B,IAAE,SAAS;AACX,MAAI,EAAE,YAAa,cAAa,GAAG,EAAE,WAAW;AACjD,CAAC;AAEK,IAAMC,WAAU,CAIrBA,aAAmG;AACpG,QAAM,WAAWA,SAAQ,UAAU,CAACA,SAAQ,MAAM,GAAGA,SAAQ,OAAO,IAAI,CAACA,SAAQ,IAAI;AAErF,QAAM,UAAe,aAAAD,SAAMC,QAAO;AAClC,MACkBA,SAAS,eAAeA,SAAQ,WAC9C,OAAO,OAAOA,SAAQ,OAAO,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE,OAAO,SAAS,YAAY,GACjF;AACD,UAAM,IAAI;AAAA,MACT,yBAAyB,IAAI,IAAI;AAAA,IAClC;AAAA,EACD;AAEA,MAAI,CAACA,SAAQ,WAAW,CAACA,SAAQ,aAAa;AAC7C,UAAM,IAAI;AAAA,MACT,yBAAyB,IAAI,IAAI;AAAA,IAClC;AAAA,EACD;AAEA,QAAM,mBAAmBA,SAAQ,UAAU,gBAAgBA,SAAQ,OAAO,IAAI;AAC9E,MAAI,UAAU;AAEd,MAAI,OAAO,IAAI,QAAQ,IAAI,SAAS,MAAM;AAE1C,MAAI,CAAC,IAAI,KAAM,OAAM,IAAI,YAAY,oCAAoC;AAEzE,MAAI,UAAU,IAAI,SAAS,SAAS,IAAI,UAAU;AAElD,MAAI,IAAI,KAAK,WAAW,GAAG,GAAG;AAC7B,UAAM,IAAI,YAAY,yBAAyB,IAAI,IAAI,wCAAwC;AAAA,EAChG;AAEA,MAAI,SAAS,QAAQ,CAAC,MAAM;AAC3B,QAAI,EAAE,WAAW,GAAG,GAAG;AACtB,YAAM,IAAI,YAAY,yBAAyB,IAAI,IAAI,2CAA2C;AAAA,IACnG;AAAA,EACD,CAAC;AAED,WAAS,QAAQ,CAAC,GAAG,MAAM;AAC1B,QAAI,MAAM,QAAQ;AACjB,YAAM,IAAI;AAAA,QACT,yBAAyB,IAAI,IAAI;AAAA,MAClC;AAAA,IACD;AAEA,UAAM,YAAY,GAAG,YAAY;AACjC,QAAI,cAAc,OAAO,cAAc,OAAO,cAAc,UAAU,cAAc,SAAS;AAC5F,YAAM,IAAI;AAAA,QACT,yBAAyB,IAAI,IAAI,QAAQ,CAAC;AAAA,MAC3C;AAAA,IACD;AAEA,UAAM,MAAM,SAAS,UAAU,CAAC,OAAO,OAAO,CAAC;AAE/C,QAAI,QAAQ,EAAG,OAAM,IAAI,YAAY,yBAAyB,IAAI,IAAI,wBAAwB,CAAC,IAAI;AAAA,EACpG,CAAC;AAED,MAAI,IAAI,aAAa;AACpB,iBAAa,KAAK,IAAI,WAAW;AAAA,EAClC;AAEA,SAAO;AACR;AAEA,IAAM,kBAAkB,CACvB,UACA,YACA,MACA,SACA,mBAC0B;AAC1B,QAAM,EAAE,MAAM,KAAK,eAAe,MAAM,IAAI,WAAW,MAAM;AAE7D,QAAMA,WAAU,SAAS,KAAK,CAAC,MAAM;AACpC,UAAM,QAAQ,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,EAAE,OAAO,IAAI,CAAC,EAAE,IAAI;AAC1D,UAAM,MAAM,MAAM,KAAK,CAAC,SAAS,SAAS,GAAG;AAE7C,WAAO;AAAA,EACR,CAAC;AAED,MAAI,CAACA,UAAS;AACb,WAAO;AAAA,MACN,SAAAA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAEA,QAAM,UAAU,cAAc,MAAM,KAAK;AAEzC,MAAI,CAAC,WAAW,UAAU,CAACA,SAAQ,aAAa;AAC/C,WAAO;AAAA,MACN,SAAAA;AAAA,MACA,MAAM;AAAA,IACP;AAAA,EACD;AAEA,QAAM,gBAAgB,WAAW,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,EAAE,gBAAgB,EAAE,EAAE;AAElG,QAAM,aAAa,gBAAgBA,SAAQ,aAAc,eAAe,SAAS,SAAS,cAAc;AAExG,MAAI,CAAC,WAAW,SAAS;AACxB,UAAM,IAAI,YAAY,QAAW;AAAA,MAChC,MAAM;AAAA,MACN,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAAA;AAAA,MACA,UAAU,WAAW,CAAC,EAAG;AAAA,IAC1B,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEA,IAAM,aAAa,CAClB,UACA,MACA,SACA,mBACI;AACJ,QAAM,aAAiC,CAAC;AAExC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACrC,UAAM,MAAM,KAAK,CAAC;AAClB,QAAI,QAAQ,YAAY,QAAQ,QAAQ,QAAQ,eAAe,QAAQ,MAAM;AAC5E,YAAM,YAAY,KAAK,IAAI,CAAC,GAAG,YAAY;AAC3C,UAAI,cAAc,OAAO,cAAc,OAAO,cAAc,UAAU,cAAc,QAAS,GAAE;AAE/F;AAAA,IACD;AAEA,QAAI,KAAK,WAAW,GAAG,GAAG;AACzB,UAAI,CAAC,IAAI,SAAS,GAAG,EAAG,GAAE;AAE1B;AAAA,IACD;AAEA,eAAW,KAAK;AAAA,MACf,MAAM;AAAA,MACN,eAAe;AAAA,IAChB,CAAC;AAAA,EACF;AAEA,MAAI,CAAC,WAAW,QAAQ;AACvB,WAAO;AAAA,MACN,SAAS;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,QAAM,iBAAiB,WAAW,CAAC;AAEnC,MAAI,eAAe,SAAS,QAAQ;AACnC,WAAO;AAAA,MACN,SAAS;AAAA,MACT,MAAM,cAAc,MAAM,eAAe,aAAa;AAAA,IACvD;AAAA,EACD;AAEA,QAAM,EAAE,SAAAA,UAAS,MAAM,QAAQ,IAAI,gBAAgB,UAAU,YAAY,MAAM,SAAS,cAAc;AAEtG,MAAI,CAACA,UAAS;AACb,UAAM,IAAI,YAAY,QAAW;AAAA,MAChC,MAAM;AAAA,MACN,WAAW;AAAA,MACX;AAAA,MACA,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU,eAAe;AAAA,IAC1B,CAAC;AAAA,EACF;AAEA,SAAO;AAAA,IACN,SAAAA;AAAA,IACA,MAAM;AAAA,EACP;AACD;AAEA,IAAM,WAAW,CAChBA,UACA,SACA,aACA,KACA,SACA,SACA,mBACI;AACJ,MAAI,OAAmB;AAEvB,QAAM,WAAW,IAAI,MAAM,GAAG;AAC9B,QAAM,QAAQ,IAAI,SAAS,GAAG;AAE9B,QAAM,WAAW,SAAS,MAAM;AAChC,QAAM,WAAW,QAAQ,SAAS,KAAK,GAAG,IAAI;AAC9C,MAAI,WAAW,CAAC;AAEhB,MAAI,aAAa,YAAY,aAAa,MAAM;AAC/C,WAAO;AAAA,MACN,QAAQ;AAAA,IACT;AAAA,EACD;AAEA,MAAI,aAAa,eAAe,aAAa,MAAM;AAClD,WAAO;AAAA,MACN,WAAW;AAAA,IACZ;AAAA,EACD;AAEA,MAAI,CAAC,IAAI,WAAW,GAAG,GAAG;AACzB,QAAI,CAAC,YAAY,OAAQ,QAAO,CAAC;AAEjC,UAAM,MAAM,YAAY,MAAM;AAE9B,QAAI,IAAI,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,QAAQ,QAAQ,GAAG,GAAG;AACnE,YAAM,IAAI,YAAY,QAAW;AAAA,QAChC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAAA;AAAA,QACA,QAAQ,IAAI,CAAC;AAAA,QACb,UAAU;AAAA,UACT,UAAU;AAAA,QACX;AAAA,MACD,CAAC;AAAA,IACF;AAEA,WAAO;AAEP,WAAO;AAAA,MACN;AAAA,MACA,UAAU;AAAA,MACV,MAAM,IAAI,CAAC;AAAA,MACX,QAAQ,IAAI,CAAC;AAAA,IACd;AAAA,EACD;AAEA,QAAM,SAAS,QAAQ,KAAK,CAAC,CAAC,QAAQ,GAAG,MAAM;AAC9C,UAAM,QAAQ,CAAC,IAAI,MAAO,GAAG,IAAI,OAAO;AAExC,QAAI,IAAI,SAAS,WAAW;AAC3B,YAAM,QAAQ,MAAM,KAAK,CAAC,SAAS,SAAS,QAAQ;AACpD,UAAI,CAAC,MAAO,QAAO;AAEnB,UAAI,YAAY,UAAU,YAAY;AAEtC,UAAI,CAAC,SAAS,SAAS,WAAW,GAAG,GAAG;AACvC,eAAO;AACP,mBAAW;AACX,eAAO;AAAA,MACR;AAEA,UAAI,cAAc,UAAa,cAAc,MAAM,cAAc,UAAU,cAAc,KAAK;AAC7F,eAAO;AACP,eAAO;AAAA,MACR;AAEA,UAAI,cAAc,WAAW,cAAc,KAAK;AAC/C,eAAO;AACP,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,OAAO;AACX,eAAO;AACP,mBAAW;AACX,eAAO;AAAA,MACR;AAEA,YAAM,IAAI,YAAY,QAAW;AAAA,QAChC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,SAAAA;AAAA,QACA,UAAU;AAAA,UACT;AAAA,UACA;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF,OAAO;AACN,YAAM,QAAQ,MAAM,KAAK,CAAC,SAAS,SAAS,QAAQ;AAEpD,UAAI,CAAC,MAAO,QAAO;AAEnB,UAAI,IAAI,SAAS,UAAU;AAC1B,YAAI,CAAC,SAAS,YAAY,QAAW;AACpC,gBAAM,IAAI,YAAY,QAAW;AAAA,YAChC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,aAAa;AAAA,YACb,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,SAAAA;AAAA,YACA,UAAU;AAAA,cACT;AAAA,cACA;AAAA,YACD;AAAA,UACD,CAAC;AAAA,QACF;AAEA,YAAI,IAAI,YAAY,CAAC,IAAI,SAAS,KAAK,CAAC,QAAQ,QAAQ,QAAQ,GAAG;AAClE,gBAAM,IAAI,YAAY,QAAW;AAAA,YAChC,MAAM;AAAA,YACN,MAAM;AAAA,YACN,aAAa;AAAA,YACb,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,SAAAA;AAAA,YACA,UAAU;AAAA,cACT;AAAA,cACA;AAAA,YACD;AAAA,UACD,CAAC;AAAA,QACF;AAEA,eAAO;AAEP,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,SAAS,YAAY,QAAW;AACpC,cAAM,IAAI,YAAY,QAAW;AAAA,UAChC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAAA;AAAA,UACA,UAAU;AAAA,YACT;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAEA,YAAM,UAAU,OAAO,QAAQ;AAE/B,UAAI,MAAM,OAAO,GAAG;AACnB,cAAM,IAAI,YAAY,QAAW;AAAA,UAChC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAAA;AAAA,UACA,UAAU;AAAA,YACT;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAEA,UAAI,IAAI,SAAS,CAAC,MAAM,OAAO,GAAG;AACjC,cAAM,IAAI,YAAY,QAAW;AAAA,UAChC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAAA;AAAA,UACA,UAAU;AAAA,YACT;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAEA,UAAI,IAAI,WAAW,UAAa,UAAU,IAAI,QAAQ;AACrD,cAAM,IAAI,YAAY,QAAW;AAAA,UAChC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAAA;AAAA,UACA,UAAU;AAAA,YACT;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAEA,UAAI,IAAI,WAAW,UAAa,UAAU,IAAI,QAAQ;AACrD,cAAM,IAAI,YAAY,QAAW;AAAA,UAChC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAAA;AAAA,UACA,UAAU;AAAA,YACT;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAEA,aAAO;AAEP,aAAO;AAAA,IACR;AAAA,EACD,CAAC;AAED,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA,MAAM,SAAS,CAAC;AAAA,IAChB,QAAQ,SAAS,CAAC;AAAA,EACnB;AACD;AAEA,IAAM,eAAe,CACpBA,UACA,MACA,SACA,gBACA,+BACiE;AACjE,QAAM,UAAUA,SAAQ;AAExB,QAAM,aAAa,OAAO,QAAQ,WAAW,CAAC,CAAuC,EAAE;AAAA,IACtF,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,MAAM;AAAA,EAChC;AAEA,QAAM,uBAAuB,WAAW,OAAO,CAAC,CAAC,KAAK,GAAG,MAAM,IAAI,SAAS,YAAY;AACxF,QAAM,oBAAoB,WAAW,OAAO,CAAC,CAAC,KAAK,GAAG,MAAM,IAAI,SAAS,YAAY;AAErF,QAAM,SAAqC,CAAC;AAE5C,QAAM,qBAAiC,CAAC;AACxC,QAAM,sBAAgC,CAAC;AAEvC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACrC,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,UAAU,KAAK,IAAI,CAAC;AAE1B,UAAM;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,IAAI,SAASA,UAAS,sBAAsB,mBAAmB,KAAK,SAAS,SAAS,cAAc;AACpG,QAAI,CAAC,OAAQ,qBAAoB,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC,CAAE;AACxD,QAAI,SAAU,GAAE;AAEhB,QAAI,OAAQ,QAAO;AACnB,QAAI,UAAW,QAAO;AAEtB,WAAO,IAAK,IAAI;AAAA,EACjB;AAEA,aAAW,CAAC,QAAQ,MAAM,KAAK,YAAY;AAC1C,UAAM,OAAO,OAAO,MAAM,KAAK,OAAO;AAEtC,QAAI,CAAC,4BAA4B;AAChC,aAAO,MAAM,IAAI;AAAA,IAClB,OAAO;AACN,UAAI,SAAS,OAAW,QAAO,MAAM,IAAI;AAAA,IAC1C;AAEA,QAAI,OAAO,cAAc,OAAO,MAAM,MAAM,OAAW,oBAAmB,KAAK,CAAC,OAAO,MAAO,GAAG,OAAO,OAAO,CAAC;AAAA,EACjH;AAEA,MAAI,mBAAmB,QAAQ;AAC9B,UAAM,IAAI,YAAY,QAAW;AAAA,MAChC,MAAM;AAAA,MACN,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAAA;AAAA,MACA,SAAS;AAAA,IACV,CAAC;AAAA,EACF;AACA,MAAI,oBAAoB,QAAQ;AAC/B,UAAM,IAAI,YAAY,QAAW;AAAA,MAChC,MAAM;AAAA,MACN,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAAA;AAAA,MACA,cAAc;AAAA,IACf,CAAC;AAAA,EACF;AAEA,SAAO,OAAO,KAAK,MAAM,EAAE,SAAS,SAAS;AAC9C;AAEO,IAAM,4BAA4B,CAACA,aACzCA,SAAQ,SAAS,GAAG,0BAA0BA,SAAQ,MAAM,CAAC,IAAIA,SAAQ,IAAI,KAAKA,SAAQ;AAE3F,IAAM,mBAAmB,CAAC,UAAqB,WAAqB;AACnE,QAAM,cAAqD,CAAC;AAE5D,aAAW,OAAO,UAAU;AAC3B,UAAM,cAAc,OAAO,OAAO,WAAW;AAE7C,eAAW,WAAW,aAAa;AAClC,YAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,MAAM,IAAI,IAAI;AAEvD,UAAI,CAAC,aAAc;AAEnB,YAAM,IAAI;AAAA,QACT,yBAAyB,0BAA0B,GAAG,CAAC,yCACtD,SAAS,GAAG,0BAA0B,MAAM,CAAC,MAAM,EACpD,GAAG,QAAQ,CAAC,CAAC;AAAA,MACd;AAAA,IACD;AAEA,QAAI,IAAI,SAAS;AAChB,iBAAW,SAAS,IAAI,SAAS;AAChC,mBAAW,WAAW,aAAa;AAClC,gBAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,MAAM,KAAK;AAEpD,cAAI,CAAC,aAAc;AAEnB,gBAAM,IAAI;AAAA,YACT,yBAAyB,0BAA0B,GAAG,CAAC,aAAa,KAAK,mCACxE,SAAS,GAAG,0BAA0B,MAAM,CAAC,MAAM,EACpD,GAAG,QAAQ,CAAC,CAAC;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,gBAAY,IAAI,IAAI,IAAI,IAAI,UACzB,CAAC,IAAI,MAAM,GAAG,IAAI,OAAO,IACzB,CAAC,IAAI,IAAI;AAEZ,QAAI,IAAI,YAAa,KAAI,cAAc,iBAAiB,IAAI,aAAa,GAAG;AAAA,EAC7E;AAEA,SAAO;AACR;AAEA,IAAM,gBAAgB,CAAI,KAAU,QAAqB,CAAC,GAAG,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC;AAS1G,IAAM,MAAM,OAAO,UAAqB,WAAyC;AACvF,QAAM,eAAe,QAAQ,QAC1B,oBAAoB,OAAO,KAAK,IAChC;AACH,QAAM,YAAY,QAAQ,aAAa,QAAQ;AAC/C,QAAM,UAAU,QAAQ;AACxB,QAAM,OAAO,QAAQ;AACrB,QAAM,6BAA6B,QAAQ,8BAA8B;AACzE,QAAM,UAAU,QAAQ;AACxB,QAAM,iBAAiB,QAAQ;AAE/B,MAAI;AACH,UAAM,gBAAgB,iBAAiB,QAAQ;AAE/C,QAAI,OAAO,UAAU,MAAM,GAAG,UAAU,MAAM;AAC9C,QAAI,CAAC,KAAK,QAAQ;AACjB,aAAO,SAAS,SAAY,MAAM,aAAa,IAAI,IAAI,MAAM,aAAa;AAAA,QACzE,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAEA,UAAM,YAAY,KAAK,UAAU,CAAC,QAAQ,QAAQ,YAAY,QAAQ,IAAI;AAC1E,QACC,cAAc,OAAO,YAAY,IAC9B,KAAK,YAAY,CAAC,GAAG,WAAW,GAAG,KAAK,CAAC,KAAK,YAAY,CAAC,EAAG,SAAS,GAAG,IAAI,QAAQ,OACtF,OACF;AACD,YAAMA,WAAU,WAAW,eAAe,MAAM,SAAS,cAAc,EAAE;AAEzE,UAAI,OAAOA,aAAY,UAAU;AAChC,eAAOA,SAAQ,SAAS,SAAY,MAAM,aAAaA,SAAQ,IAAI,IAAI,MAAM,aAAa;AAAA,UACzF,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,SAASA;AAAA,QACV,CAAC;AAAA,MACF,OAAO;AACN,eAAO,SAAS,SAAY,MAAM,aAAa,IAAI,IAAI,MAAM,aAAa;AAAA,UACzE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAAA,MACF;AAAA,IACD;AAEA,UAAM,eAAe,KAAK,UAAU,CAAC,QAAQ,QAAQ,eAAe,QAAQ,IAAI;AAChF,QAAI,iBAAiB,OAAO,eAAe,IAAI,KAAK,eAAe,CAAC,GAAG,WAAW,GAAG,IAAI,QAAQ,OAAO,OAAO;AAC9G,aAAO,YAAY,SAAY,MAAM,aAAa,OAAO,IAAI,MAAM,aAAa;AAAA,QAC/E,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAEA,UAAM,EAAE,SAAAA,UAAS,MAAM,QAAQ,IAAI,WAAW,eAAe,MAAM,SAAS,cAAc;AAE1F,QAAI,CAACA,UAAS;AACb,aAAO,SAAS,SAAY,MAAM,aAAa,IAAI,IAAI,MAAM,aAAa;AAAA,QACzE,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAEA,QAAIA,aAAY,QAAQ;AACvB,UAAI;AACJ,UAAI,aAAuB;AAE3B,SAAG;AACF,cAAM,MAAM,WAAW,eAAe,YAAY,SAAS,cAAc;AACzE,sBAAc,IAAI;AAClB,qBAAa,IAAI;AAAA,MAClB,SAAS,gBAAgB;AAEzB,aAAO,cACJ,YAAY,SAAS,SAAY,MAAM,aAAa,YAAY,IAAI,IAAI,MAAM,aAAa;AAAA,QAC5F,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,MACV,CAAC,IACC,SAAS,SACT,MAAM,aAAa,IAAI,IACvB,MAAM,aAAa;AAAA,QACpB,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,eAAe,aAAaA,UAAS,SAAS,SAAS,gBAAgB,0BAA0B;AAEvG,QAAI,iBAAiB,QAAQ;AAC5B,aAAOA,SAAQ,SAAS,SAAY,MAAM,aAAaA,SAAQ,IAAI,IAAI,MAAM,aAAa;AAAA,QACzF,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAASA;AAAA,MACV,CAAC;AAAA,IACF;AACA,QAAI,iBAAiB,WAAW;AAC/B,aAAO,YAAY,SAAY,MAAM,aAAa,OAAO,IAAI,MAAM,aAAa;AAAA,QAC/E,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAEA,QAAIA,SAAQ,SAAS;AACpB,UAAI,QAAQ,KAAM,OAAM,OAAO,KAAK,UAAUA,QAAO;AACrD,YAAMA,SAAQ,QAAQA,SAAQ,YAAY,MAAMA,SAAQ,UAAU,YAAY,IAAI,YAAY;AAC9F,UAAI,QAAQ,KAAM,OAAM,OAAO,KAAK,SAASA,QAAO;AACpD;AAAA,IACD,OAAO;AACN,aAAOA,SAAQ,SAAS,SAAY,MAAM,aAAaA,SAAQ,IAAI,IAAI,MAAM,aAAa;AAAA,QACzF,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAASA;AAAA,MACV,CAAC;AAAA,IACF;AAAA,EACD,SAAS,GAAG;AACX,QAAI,aAAa,aAAa;AAC7B,UAAI,EAAE,MAAO,OAAM,aAAa,EAAE,KAAK;AAAA,WAClC;AAEJ,YAAI,CAAC,QAAQ,OAAQ,SAAQ,MAAM,EAAE,OAAO;AAAA,YAEvC,QAAO,EAAE;AAAA,MACf;AAAA,IACD,OAAO;AACN,YAAM,aAAa;AAAA,QAClB,MAAM;AAAA,QACN,WAAW;AAAA,QACX,MAAM;AAAA,QACN,aAAa;AAAA,QACb,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAGA,QAAI,CAAC,QAAQ,OAAQ,SAAQ,KAAK,CAAC;AAEnC;AAAA,EACD;AACD;AAEO,IAAM,UAAU,CACtB,SACAC,aACIA;AAEE,IAAM,OAAO,OACnBD,UACA,SACwC;AACxC,MAAI;AACH,UAAM,gBAA0B,UAAU,IAAI;AAC9C,UAAM,UAAU,aAAaA,UAAS,eAAe,QAAW,MAAS;AAEzE,QAAI,YAAY,UAAU,YAAY,WAAW;AAChD,aAAO;AAAA,QACN,MAAM;AAAA,MACP;AAAA,IACD;AAEA,WAAO;AAAA,MACN,SAASA,SAAQ,YAAY,MAAMA,SAAQ,UAAU,OAAO,IAAI;AAAA,MAChE,MAAM;AAAA,IACP;AAAA,EACD,SAAS,GAAG;AACX,WAAO;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACD;AACD;AAEO,IAAM,eAAe,CAC3B,aACkB;AAClB,QAAM,YAAY,iBAAiB,QAAQ;AAE3C,SAAO,OAAO,YAAY,UAAU,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM;AAAA,IACvD,MAAM,EAAE;AAAA,IACR,aAAS,aAAAD,SAAM,EAAE,OAAO;AAAA,IACxB,MAAM,EAAE;AAAA,IACR,WAAW,EAAE;AAAA,IACb,UAAU,EAAE;AAAA,IACZ,SAAS,EAAE,UACR,OAAO,YAAY,OAAO,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,SAAK,aAAAA,SAAM,IAAI,MAAM,CAAC,CAAC,CAAC,IAC1F;AAAA,IACH,cAAU,aAAAA,SAAM,EAAE,QAAQ;AAAA,IAC1B,aAAa,EAAE,cAAc,aAAa,EAAE,WAAW,IAAI;AAAA,EAC5D,CAAC,CAAC,CAAC;AACJ;;;AG36BO,IAAM,oBAAN,MAAM,mBAKX;AAAA,EACM;AAAA,EAUC,SAAS,MAAsB,KAAK,EAAE;AAAA,EAE9C,YAAY,QAAyB;AACpC,SAAK,IAAI;AAAA,MACR,QAAQ,UAAU;AAAA,QACjB,SAAS,CAAC;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACA,SAAS;AAAA,IACV;AAAA,EACD;AAAA,EAkBO,OACN,MACC;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,MAAM,UAAU,KAAW,CAAC;AAAA,EACvE;AAAA,EAkBO,OACN,MACC;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,MAAM,UAAU,KAAW,CAAC;AAAA,EACvE;AAAA,EAkBO,QACN,MACC;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,MAAM,WAAW,KAAW,CAAC;AAAA,EACxE;AAAA,EAkBO,WAAW,aAAsB;AACvC,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,MAAM,cAAc,MAAM,YAAY,CAAC;AAAA,EAClF;AAAA,EAEO,SACH,SAQF;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,QAAQ,CAAC;AAAA,EACpD;AAAA,EAEO,KAAkC,aAOvC;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,YAAY,CAAC;AAAA,EACxD;AAAA,EAEO,SAOL;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,UAAU,KAAK,CAAC;AAAA,EAC3D;AAAA,EAEO,WAOL;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,YAAY,KAAK,CAAC;AAAA,EAC7D;AAAA,EAEO,QAAyF,OAQ9F;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,UAAM,QAAQ,OAAO;AACrB,QAAI,SAAS,CAAC,MAAM,KAAK,CAAC,MAAM,UAAU,CAAC,GAAG;AAC7C,YAAM,IAAI;AAAA,QACT,kBAAkB,MAAM,KAAK,IAAI,CAAC,0CAA0C,KAAK;AAAA,MAClF;AAAA,IACD;AAEA,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,SAAS,MAAM,CAAC;AAAA,EAC3D;AAAA,EAEO,QACH,QASF;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,UAAM,aAAa,OAAO;AAC1B,QAAI,eAAe,UAAa,CAAC,OAAO,KAAK,CAAC,MAAM,eAAe,CAAC,GAAG;AACtE,YAAM,IAAI;AAAA,QACT,kBAAkB,OAAO,KAAK,IAAI,CAAC,0CAA0C,UAAU;AAAA,MACxF;AAAA,IACD;AAEA,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,UAAU,OAAO,CAAC;AAAA,EAC7D;AAAA,EAEO,IAAI,OAOT;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,UAAM,SAAS,OAAO;AACtB,QAAI,WAAW,UAAa,SAAS,OAAO;AAC3C,YAAM,IAAI,YAAY,kEAAkE;AAAA,IACzF;AAEA,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC1D;AAAA,EAEO,IAAI,OAOT;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,UAAM,SAAS,OAAO;AACtB,QAAI,WAAW,UAAa,SAAS,OAAO;AAC3C,YAAM,IAAI,YAAY,iEAAiE;AAAA,IACxF;AAEA,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC1D;AAAA,EAEO,MAOL;AACD,UAAM,SAAS,KAAK,OAAO;AAE3B,WAAO,IAAI,mBAAkB,EAAE,GAAG,QAAQ,OAAO,KAAK,CAAC;AAAA,EACxD;AACD;AA2EO,SAAS,OAA6B,MAAc;AAC1D,SAAO,OAAO,SAAS,WAAW,IAAI,kBAAkB,EAAE,OAAO,IAAI,IAAI,IAAI,kBAAkB,EAAE,OAAO;AACzG;AAoBO,SAAS,OAA6B,MAAc;AAC1D,SAAO,OAAO,SAAS,WAAW,IAAI,kBAAkB,EAAE,OAAO,IAAI,IAAI,IAAI,kBAAkB,EAAE,OAAO;AACzG;AAoBO,SAAS,QAA8B,MAAc;AAC3D,SAAO,OAAO,SAAS,WAAW,IAAI,kBAAkB,EAAE,QAAQ,IAAI,IAAI,IAAI,kBAAkB,EAAE,QAAQ;AAC3G;AAkBO,SAAS,WAAW,aAAsB;AAChD,SAAO,OAAO,gBAAgB,WAC3B,IAAI,kBAAkB,EAAE,WAAW,WAAW,IAC9C,IAAI,kBAAkB,EAAE,WAAW;AACvC;", "names": ["clone", "parent", "depth", "string", "i", "s", "command", "s", "desc", "msg", "parseQuotes", "clone", "command", "handler"]}