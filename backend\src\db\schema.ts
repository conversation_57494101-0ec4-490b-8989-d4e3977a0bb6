import { sqliteTable, text, integer, real, index, uniqueIndex } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// 用户表 - 管理员、分销商等
export const users = sqliteTable('users', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  username: text('username').notNull(),
  passwordHash: text('passwordHash').notNull(),
  role: text('role').notNull(), // 'ADMIN' | 'DISTRIBUTOR'
  status: text('status').notNull().default('ACTIVE'), // 'ACTIVE' | 'INACTIVE' | 'SUSPENDED'
  nickName: text('nickName'),
  wechat: text('wechat'),
  avatar: text('avatar'),
  createdAt: text('createdAt').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updatedAt').notNull()
}, (table) => ({
  // 唯一索引
  usernameIdx: uniqueIndex('users_username_key').on(table.username),
  
  // 普通索引
  roleIdx: index('users_role_idx').on(table.role),
  statusIdx: index('users_status_idx').on(table.status),
  createdAtIdx: index('users_createdAt_idx').on(table.createdAt),
  
  // 复合索引
  roleStatusIdx: index('users_role_status_idx').on(table.role, table.status)
}));

// 产品表
export const products = sqliteTable('products', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  description: text('description'),
  category: text('category'),
  status: text('status').notNull().default('ACTIVE'), // 'ACTIVE' | 'INACTIVE'
  createdAt: text('createdAt').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updatedAt').notNull()
}, (table) => ({
  // 唯一索引
  nameIdx: uniqueIndex('products_name_key').on(table.name),
  
  // 普通索引
  statusIdx: index('products_status_idx').on(table.status),
  categoryIdx: index('products_category_idx').on(table.category),
  createdAtIdx: index('products_createdAt_idx').on(table.createdAt)
}));

// 产品版本表
export const productVersions = sqliteTable('product_versions', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  productId: integer('productId').notNull().references(() => products.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
  version: text('version').notNull(),
  versionName: text('versionName'),
  description: text('description'),
  configTemplate: text('configTemplate').notNull(), // JSON配置模板
  encryptionKey: text('encryptionKey').notNull(), // 加密密钥
  defaultPrice: real('defaultPrice').notNull(),
  downloadLink: text('downloadLink'),
  coverUrl: text('coverUrl'),
  changelog: text('changelog'),
  status: text('status').notNull().default('ACTIVE'), // 'ACTIVE' | 'INACTIVE'
  createdAt: text('createdAt').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updatedAt').notNull()
}, (table) => ({
  // 普通索引
  productIdIdx: index('product_versions_productId_idx').on(table.productId),
  statusIdx: index('product_versions_status_idx').on(table.status),
  createdAtIdx: index('product_versions_createdAt_idx').on(table.createdAt),
  
  // 复合索引
  productIdStatusIdx: index('product_versions_productId_status_idx').on(table.productId, table.status),
  
  // 唯一复合索引
  productIdVersionIdx: uniqueIndex('product_versions_productId_version_key').on(table.productId, table.version)
}));

// 授权表 - 分销商对产品版本的授权
export const authorizations = sqliteTable('authorizations', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  distributorId: integer('distributorId').notNull().references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
  versionId: integer('versionId').notNull().references(() => productVersions.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
  customPrice: real('customPrice'), // 自定义价格，如果为空则使用默认价格
  status: text('status').notNull().default('ACTIVE'), // 'ACTIVE' | 'INACTIVE'
  createdAt: text('createdAt').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updatedAt').notNull()
}, (table) => ({
  // 普通索引
  distributorIdIdx: index('authorizations_distributorId_idx').on(table.distributorId),
  versionIdIdx: index('authorizations_versionId_idx').on(table.versionId),
  statusIdx: index('authorizations_status_idx').on(table.status),
  createdAtIdx: index('authorizations_createdAt_idx').on(table.createdAt),
  
  // 复合索引
  distributorIdStatusIdx: index('authorizations_distributorId_status_idx').on(table.distributorId, table.status),
  
  // 唯一复合索引
  distributorIdVersionIdIdx: uniqueIndex('authorizations_distributorId_versionId_key').on(table.distributorId, table.versionId)
}));

// 许可证表
export const licenses = sqliteTable('licenses', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  versionId: integer('versionId').notNull().references(() => productVersions.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
  licenseKey: text('licenseKey').notNull(),
  status: text('status').notNull().default('INACTIVE'), // 'ACTIVE' | 'INACTIVE' | 'EXPIRED' | 'REVOKED'
  verifyConfig: text('verifyConfig'), // JSON验证配置
  activatedAt: text('activatedAt'),
  distributorId: integer('distributorId').notNull().references(() => users.id, { onDelete: 'restrict', onUpdate: 'cascade' }),
  createdAt: text('createdAt').notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updatedAt').notNull()
}, (table) => ({
  // 唯一索引
  licenseKeyIdx: uniqueIndex('licenses_licenseKey_key').on(table.licenseKey),
  
  // 普通索引
  licenseKeySearchIdx: index('licenses_licenseKey_idx').on(table.licenseKey),
  versionIdIdx: index('licenses_versionId_idx').on(table.versionId),
  distributorIdIdx: index('licenses_distributorId_idx').on(table.distributorId),
  statusIdx: index('licenses_status_idx').on(table.status),
  activatedAtIdx: index('licenses_activatedAt_idx').on(table.activatedAt),
  createdAtIdx: index('licenses_createdAt_idx').on(table.createdAt),
  
  // 复合索引
  distributorIdStatusIdx: index('licenses_distributorId_status_idx').on(table.distributorId, table.status),
  statusCreatedAtIdx: index('licenses_status_createdAt_idx').on(table.status, table.createdAt)
}));

// 导出表类型，用于TypeScript类型推断
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Product = typeof products.$inferSelect;
export type NewProduct = typeof products.$inferInsert;

export type ProductVersion = typeof productVersions.$inferSelect;
export type NewProductVersion = typeof productVersions.$inferInsert;

export type Authorization = typeof authorizations.$inferSelect;
export type NewAuthorization = typeof authorizations.$inferInsert;

export type License = typeof licenses.$inferSelect;
export type NewLicense = typeof licenses.$inferInsert;
