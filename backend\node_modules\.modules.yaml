hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@cloudflare/kv-asset-handler@0.4.0':
    '@cloudflare/kv-asset-handler': private
  '@cloudflare/unenv-preset@2.5.0(unenv@2.0.0-rc.19)(workerd@1.20250730.0)':
    '@cloudflare/unenv-preset': private
  '@cloudflare/workerd-darwin-64@1.20250730.0':
    '@cloudflare/workerd-darwin-64': private
  '@cloudflare/workerd-darwin-arm64@1.20250730.0':
    '@cloudflare/workerd-darwin-arm64': private
  '@cloudflare/workerd-linux-64@1.20250730.0':
    '@cloudflare/workerd-linux-64': private
  '@cloudflare/workerd-linux-arm64@1.20250730.0':
    '@cloudflare/workerd-linux-arm64': private
  '@cloudflare/workerd-windows-64@1.20250730.0':
    '@cloudflare/workerd-windows-64': private
  '@cloudflare/workers-types@4.20250214.0':
    '@cloudflare/workers-types': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@drizzle-team/brocli@0.10.2':
    '@drizzle-team/brocli': private
  '@esbuild-kit/core-utils@3.3.2':
    '@esbuild-kit/core-utils': private
  '@esbuild-kit/esm-loader@2.6.5':
    '@esbuild-kit/esm-loader': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.8':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@poppinss/colors@4.1.5':
    '@poppinss/colors': private
  '@poppinss/dumper@0.6.4':
    '@poppinss/dumper': private
  '@poppinss/exception@1.2.2':
    '@poppinss/exception': private
  '@prisma/config@6.13.0':
    '@prisma/config': private
  '@prisma/debug@6.13.0':
    '@prisma/debug': private
  '@prisma/driver-adapter-utils@6.13.0':
    '@prisma/driver-adapter-utils': private
  '@prisma/engines-version@6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd':
    '@prisma/engines-version': private
  '@prisma/engines@6.13.0':
    '@prisma/engines': private
  '@prisma/fetch-engine@6.13.0':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@6.13.0':
    '@prisma/get-platform': private
  '@rollup/rollup-android-arm-eabi@4.46.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.46.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.46.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.46.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.46.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.46.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.46.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.46.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.46.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.46.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.46.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-ppc64-gnu@4.46.2':
    '@rollup/rollup-linux-ppc64-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.46.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.46.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.46.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.46.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.46.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.46.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.46.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.46.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@sindresorhus/is@7.0.2':
    '@sindresorhus/is': private
  '@speed-highlight/core@1.2.7':
    '@speed-highlight/core': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@types/chai@5.2.2':
    '@types/chai': private
  '@types/deep-eql@4.0.2':
    '@types/deep-eql': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@vitest/expect@3.2.4':
    '@vitest/expect': private
  '@vitest/mocker@3.2.4(vite@7.0.6(jiti@2.5.1))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.2.4':
    '@vitest/pretty-format': private
  '@vitest/runner@3.2.4':
    '@vitest/runner': private
  '@vitest/snapshot@3.2.4':
    '@vitest/snapshot': private
  '@vitest/spy@3.2.4':
    '@vitest/spy': private
  '@vitest/utils@3.2.4':
    '@vitest/utils': private
  acorn-walk@8.3.2:
    acorn-walk: private
  acorn@8.14.0:
    acorn: private
  assertion-error@2.0.1:
    assertion-error: private
  birpc@0.2.14:
    birpc: private
  blake3-wasm@2.1.5:
    blake3-wasm: private
  buffer-from@1.1.2:
    buffer-from: private
  c12@3.1.0:
    c12: private
  cac@6.7.14:
    cac: private
  chai@5.2.1:
    chai: private
  check-error@2.1.1:
    check-error: private
  chokidar@4.0.3:
    chokidar: private
  citty@0.1.6:
    citty: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  confbox@0.2.2:
    confbox: private
  consola@3.4.2:
    consola: private
  cookie@1.0.2:
    cookie: private
  debug@4.4.1:
    debug: private
  deep-eql@5.0.2:
    deep-eql: private
  deepmerge-ts@7.1.5:
    deepmerge-ts: private
  defu@6.1.4:
    defu: private
  destr@2.0.5:
    destr: private
  detect-libc@2.0.4:
    detect-libc: private
  devalue@4.3.3:
    devalue: private
  dotenv@16.6.1:
    dotenv: private
  effect@3.16.12:
    effect: private
  error-stack-parser-es@1.0.5:
    error-stack-parser-es: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  esbuild-register@3.6.0(esbuild@0.25.8):
    esbuild-register: private
  esbuild@0.25.8:
    esbuild: private
  estree-walker@3.0.3:
    estree-walker: private
  exit-hook@2.2.1:
    exit-hook: private
  expect-type@1.2.2:
    expect-type: private
  exsolve@1.0.7:
    exsolve: private
  fast-check@3.23.2:
    fast-check: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  find-up-simple@1.0.1:
    find-up-simple: private
  fsevents@2.3.3:
    fsevents: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  giget@2.0.0:
    giget: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  hosted-git-info@7.0.2:
    hosted-git-info: private
  index-to-position@1.1.0:
    index-to-position: private
  is-arrayish@0.3.2:
    is-arrayish: private
  jiti@2.5.1:
    jiti: private
  js-tokens@9.0.1:
    js-tokens: private
  kleur@4.1.5:
    kleur: private
  ky@1.7.5:
    ky: private
  loupe@3.2.0:
    loupe: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  mime@3.0.0:
    mime: private
  miniflare@4.20250730.0:
    miniflare: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  normalize-package-data@6.0.2:
    normalize-package-data: private
  nypm@0.6.1:
    nypm: private
  ohash@2.0.11:
    ohash: private
  parse-json@8.3.0:
    parse-json: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.1:
    pathval: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pkg-types@2.2.0:
    pkg-types: private
  postcss@8.5.6:
    postcss: private
  pure-rand@6.1.0:
    pure-rand: private
  rc9@2.1.2:
    rc9: private
  read-package-up@11.0.0:
    read-package-up: private
  read-pkg@9.0.1:
    read-pkg: private
  readdirp@4.1.2:
    readdirp: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  rollup@4.46.2:
    rollup: private
  semver@7.7.2:
    semver: private
  sharp@0.33.5:
    sharp: private
  siginfo@2.0.0:
    siginfo: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  stackback@0.0.2:
    stackback: private
  std-env@3.9.0:
    std-env: private
  stoppable@1.1.0:
    stoppable: private
  strip-literal@3.0.0:
    strip-literal: private
  supports-color@10.0.0:
    supports-color: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinypool@1.1.1:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@4.0.3:
    tinyspy: private
  type-fest@4.41.0:
    type-fest: private
  ufo@1.6.1:
    ufo: private
  undici@7.13.0:
    undici: private
  unenv@2.0.0-rc.19:
    unenv: private
  unicorn-magic@0.1.0:
    unicorn-magic: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vite-node@3.2.4(jiti@2.5.1):
    vite-node: private
  vite@7.0.6(jiti@2.5.1):
    vite: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  workerd@1.20250730.0:
    workerd: private
  ws@8.18.0:
    ws: private
  youch-core@0.3.3:
    youch-core: private
  youch@4.1.0-beta.10:
    youch: private
  zod@3.25.76:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.10.0
pendingBuilds: []
prunedAt: Fri, 01 Aug 2025 19:14:16 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@cloudflare/workerd-darwin-64@1.20250718.0'
  - '@cloudflare/workerd-darwin-64@1.20250730.0'
  - '@cloudflare/workerd-darwin-arm64@1.20250718.0'
  - '@cloudflare/workerd-darwin-arm64@1.20250730.0'
  - '@cloudflare/workerd-linux-64@1.20250718.0'
  - '@cloudflare/workerd-linux-64@1.20250730.0'
  - '@cloudflare/workerd-linux-arm64@1.20250718.0'
  - '@cloudflare/workerd-linux-arm64@1.20250730.0'
  - '@emnapi/runtime@1.4.5'
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.17.19'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.17.19'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.17.19'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.17.19'
  - '@esbuild/darwin-arm64@0.18.20'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.17.19'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.17.19'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.17.19'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.17.19'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.17.19'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.17.19'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.17.19'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.17.19'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.17.19'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.17.19'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.17.19'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.17.19'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.17.19'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.17.19'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.17.19'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.17.19'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.17.19'
  - '@esbuild/win32-ia32@0.18.20'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-ia32@0.25.8'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - fsevents@2.3.3
  - tslib@2.8.1
storeDir: C:\Users\<USER>\AppData\Roaming\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\code\verify\backend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
