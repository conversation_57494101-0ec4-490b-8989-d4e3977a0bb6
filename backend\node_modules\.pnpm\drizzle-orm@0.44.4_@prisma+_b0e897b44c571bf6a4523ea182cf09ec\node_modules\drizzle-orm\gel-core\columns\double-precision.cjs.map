{"version": 3, "sources": ["../../../src/gel-core/columns/double-precision.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelDoublePrecisionBuilderInitial<TName extends string> = GelDoublePrecisionBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'GelDoublePrecision';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class GelDoublePrecisionBuilder<T extends ColumnBuilderBaseConfig<'number', 'GelDoublePrecision'>>\n\textends GelColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelDoublePrecisionBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'GelDoublePrecision');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelDoublePrecision<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelDoublePrecision<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelDoublePrecision<T extends ColumnBaseConfig<'number', 'GelDoublePrecision'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelDoublePrecision';\n\n\tgetSQLType(): string {\n\t\treturn 'double precision';\n\t}\n\n\toverride mapFromDriverValue(value: string | number): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number.parseFloat(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function doublePrecision(): GelDoublePrecisionBuilderInitial<''>;\nexport function doublePrecision<TName extends string>(name: TName): GelDoublePrecisionBuilderInitial<TName>;\nexport function doublePrecision(name?: string) {\n\treturn new GelDoublePrecisionBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA4C;AAWrC,MAAM,kCACJ,+BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,oBAAoB;AAAA,EAC3C;AAAA;AAAA,EAGS,MACR,OACsD;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BAAuF,wBAAa;AAAA,EAChH,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,WAAW,KAAK;AAAA,IAC/B;AACA,WAAO;AAAA,EACR;AACD;AAIO,SAAS,gBAAgB,MAAe;AAC9C,SAAO,IAAI,0BAA0B,QAAQ,EAAE;AAChD;", "names": []}