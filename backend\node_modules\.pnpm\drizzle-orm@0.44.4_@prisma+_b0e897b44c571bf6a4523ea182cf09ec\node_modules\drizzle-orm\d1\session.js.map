{"version": 3, "sources": ["../../src/d1/session.ts"], "sourcesContent": ["/// <reference types=\"@cloudflare/workers-types\" />\n\nimport type { BatchItem } from '~/batch.ts';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport type { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport { SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport type {\n\tPreparedQueryConfig as PreparedQueryConfigBase,\n\tSQLiteExecuteMethod,\n\tSQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { SQLitePreparedQuery, SQLiteSession } from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport interface SQLiteD1SessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class SQLiteD1Session<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteSession<'async', D1Result, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLiteD1Session';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: D1Database,\n\t\tdialect: SQLiteAsyncDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: SQLiteD1SessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): D1PreparedQuery {\n\t\tconst stmt = this.client.prepare(query.sql);\n\t\treturn new D1PreparedQuery(\n\t\t\tstmt,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync batch<T extends BatchItem<'sqlite'>[] | readonly BatchItem<'sqlite'>[]>(queries: T) {\n\t\tconst preparedQueries: PreparedQuery[] = [];\n\t\tconst builtQueries: D1PreparedStatement[] = [];\n\n\t\tfor (const query of queries) {\n\t\t\tconst preparedQuery = query._prepare();\n\t\t\tconst builtQuery = preparedQuery.getQuery();\n\t\t\tpreparedQueries.push(preparedQuery);\n\t\t\tif (builtQuery.params.length > 0) {\n\t\t\t\tbuiltQueries.push((preparedQuery as D1PreparedQuery).stmt.bind(...builtQuery.params));\n\t\t\t} else {\n\t\t\t\tconst builtQuery = preparedQuery.getQuery();\n\t\t\t\tbuiltQueries.push(\n\t\t\t\t\tthis.client.prepare(builtQuery.sql).bind(...builtQuery.params),\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst batchResults = await this.client.batch<any>(builtQueries);\n\t\treturn batchResults.map((result, i) => preparedQueries[i]!.mapResult(result, true));\n\t}\n\n\toverride extractRawAllValueFromBatchResult(result: unknown): unknown {\n\t\treturn (result as D1Result).results;\n\t}\n\n\toverride extractRawGetValueFromBatchResult(result: unknown): unknown {\n\t\treturn (result as D1Result).results[0];\n\t}\n\n\toverride extractRawValuesValueFromBatchResult(result: unknown): unknown {\n\t\treturn d1ToRawMapping((result as D1Result).results);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: D1Transaction<TFullSchema, TSchema>) => T | Promise<T>,\n\t\tconfig?: SQLiteTransactionConfig,\n\t): Promise<T> {\n\t\tconst tx = new D1Transaction('async', this.dialect, this, this.schema);\n\t\tawait this.run(sql.raw(`begin${config?.behavior ? ' ' + config.behavior : ''}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait this.run(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait this.run(sql`rollback`);\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class D1Transaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteTransaction<'async', D1Result, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'D1Transaction';\n\n\toverride async transaction<T>(transaction: (tx: D1Transaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex}`;\n\t\tconst tx = new D1Transaction('async', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tawait this.session.run(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait this.session.run(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait this.session.run(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\n/**\n * This function was taken from the D1 implementation: https://github.com/cloudflare/workerd/blob/****************************************/src/cloudflare/internal/d1-api.ts#L287\n * It may cause issues with duplicated column names in join queries, which should be fixed on the D1 side.\n * @param results\n * @returns\n */\nfunction d1ToRawMapping(results: any) {\n\tconst rows: unknown[][] = [];\n\tfor (const row of results) {\n\t\tconst entry = Object.keys(row).map((k) => row[k]);\n\t\trows.push(entry);\n\t}\n\treturn rows;\n}\n\nexport class D1PreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends SQLitePreparedQuery<\n\t{ type: 'async'; run: D1Response; all: T['all']; get: T['get']; values: T['values']; execute: T['execute'] }\n> {\n\tstatic override readonly [entityKind]: string = 'D1PreparedQuery';\n\n\t/** @internal */\n\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => unknown;\n\n\t/** @internal */\n\tfields?: SelectedFieldsOrdered;\n\n\t/** @internal */\n\tstmt: D1PreparedStatement;\n\n\tconstructor(\n\t\tstmt: D1PreparedStatement,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t) {\n\t\tsuper('async', executeMethod, query, cache, queryMetadata, cacheConfig);\n\t\tthis.customResultMapper = customResultMapper;\n\t\tthis.fields = fields;\n\t\tthis.stmt = stmt;\n\t}\n\n\tasync run(placeholderValues?: Record<string, unknown>): Promise<D1Response> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn await this.queryWithCache(this.query.sql, params, async () => {\n\t\t\treturn this.stmt.bind(...params).run();\n\t\t});\n\t}\n\n\tasync all(placeholderValues?: Record<string, unknown>): Promise<T['all']> {\n\t\tconst { fields, query, logger, stmt, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\t\t\treturn await this.queryWithCache(query.sql, params, async () => {\n\t\t\t\treturn stmt.bind(...params).all().then(({ results }) => this.mapAllResult(results!));\n\t\t\t});\n\t\t}\n\n\t\tconst rows = await this.values(placeholderValues);\n\n\t\treturn this.mapAllResult(rows);\n\t}\n\n\toverride mapAllResult(rows: unknown, isFromBatch?: boolean): unknown {\n\t\tif (isFromBatch) {\n\t\t\trows = d1ToRawMapping((rows as D1Result).results);\n\t\t}\n\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn rows;\n\t\t}\n\n\t\tif (this.customResultMapper) {\n\t\t\treturn this.customResultMapper(rows as unknown[][]);\n\t\t}\n\n\t\treturn (rows as unknown[][]).map((row) => mapResultRow(this.fields!, row, this.joinsNotNullableMap));\n\t}\n\n\tasync get(placeholderValues?: Record<string, unknown>): Promise<T['get']> {\n\t\tconst { fields, joinsNotNullableMap, query, logger, stmt, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\t\t\treturn await this.queryWithCache(query.sql, params, async () => {\n\t\t\t\treturn stmt.bind(...params).all().then(({ results }) => results![0]);\n\t\t\t});\n\t\t}\n\n\t\tconst rows = await this.values(placeholderValues);\n\n\t\tif (!rows[0]) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows) as T['all'];\n\t\t}\n\n\t\treturn mapResultRow(fields!, rows[0], joinsNotNullableMap);\n\t}\n\n\toverride mapGetResult(result: unknown, isFromBatch?: boolean): unknown {\n\t\tif (isFromBatch) {\n\t\t\tresult = d1ToRawMapping((result as D1Result).results)[0];\n\t\t}\n\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn result;\n\t\t}\n\n\t\tif (this.customResultMapper) {\n\t\t\treturn this.customResultMapper([result as unknown[]]) as T['all'];\n\t\t}\n\n\t\treturn mapResultRow(this.fields!, result as unknown[], this.joinsNotNullableMap);\n\t}\n\n\tasync values<T extends any[] = unknown[]>(placeholderValues?: Record<string, unknown>): Promise<T[]> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn await this.queryWithCache(this.query.sql, params, async () => {\n\t\t\treturn this.stmt.bind(...params).raw();\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n"], "mappings": "AAGA,SAAqB,iBAAiB;AAEtC,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAG3B,SAAS,kBAA8B,WAAW;AAElD,SAAS,yBAAyB;AAOlC,SAAS,qBAAqB,qBAAqB;AACnD,SAAS,oBAAoB;AAStB,MAAM,wBAGH,cAAuD;AAAA,EAMhE,YACS,QACR,SACQ,QACA,UAAkC,CAAC,GAC1C;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,UAAU;AAAA,EAC7C;AAAA,EAdA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,eACA,uBACA,oBACA,eAIA,aACkB;AAClB,UAAM,OAAO,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC1C,WAAO,IAAI;AAAA,MACV;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAwE,SAAY;AACzF,UAAM,kBAAmC,CAAC;AAC1C,UAAM,eAAsC,CAAC;AAE7C,eAAW,SAAS,SAAS;AAC5B,YAAM,gBAAgB,MAAM,SAAS;AACrC,YAAM,aAAa,cAAc,SAAS;AAC1C,sBAAgB,KAAK,aAAa;AAClC,UAAI,WAAW,OAAO,SAAS,GAAG;AACjC,qBAAa,KAAM,cAAkC,KAAK,KAAK,GAAG,WAAW,MAAM,CAAC;AAAA,MACrF,OAAO;AACN,cAAMA,cAAa,cAAc,SAAS;AAC1C,qBAAa;AAAA,UACZ,KAAK,OAAO,QAAQA,YAAW,GAAG,EAAE,KAAK,GAAGA,YAAW,MAAM;AAAA,QAC9D;AAAA,MACD;AAAA,IACD;AAEA,UAAM,eAAe,MAAM,KAAK,OAAO,MAAW,YAAY;AAC9D,WAAO,aAAa,IAAI,CAAC,QAAQ,MAAM,gBAAgB,CAAC,EAAG,UAAU,QAAQ,IAAI,CAAC;AAAA,EACnF;AAAA,EAES,kCAAkC,QAA0B;AACpE,WAAQ,OAAoB;AAAA,EAC7B;AAAA,EAES,kCAAkC,QAA0B;AACpE,WAAQ,OAAoB,QAAQ,CAAC;AAAA,EACtC;AAAA,EAES,qCAAqC,QAA0B;AACvE,WAAO,eAAgB,OAAoB,OAAO;AAAA,EACnD;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,UAAM,KAAK,IAAI,cAAc,SAAS,KAAK,SAAS,MAAM,KAAK,MAAM;AACrE,UAAM,KAAK,IAAI,IAAI,IAAI,QAAQ,QAAQ,WAAW,MAAM,OAAO,WAAW,EAAE,EAAE,CAAC;AAC/E,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,KAAK,IAAI,WAAW;AAC1B,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,KAAK,IAAI,aAAa;AAC5B,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,sBAGH,kBAA2D;AAAA,EACpE,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAe,YAAe,aAAkF;AAC/G,UAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,UAAM,KAAK,IAAI,cAAc,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AACnG,UAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AAC5D,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AACpE,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AACxE,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAQA,SAAS,eAAe,SAAc;AACrC,QAAM,OAAoB,CAAC;AAC3B,aAAW,OAAO,SAAS;AAC1B,UAAM,QAAQ,OAAO,KAAK,GAAG,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;AAChD,SAAK,KAAK,KAAK;AAAA,EAChB;AACA,SAAO;AACR;AAEO,MAAM,wBAA6E,oBAExF;AAAA,EAYD,YACC,MACA,OACQ,QACR,OACA,eAIA,aACA,QACA,eACQ,wBACR,oBACC;AACD,UAAM,SAAS,eAAe,OAAO,OAAO,eAAe,WAAW;AAZ9D;AASA;AAIR,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EA9BA,QAA0B,UAAU,IAAY;AAAA;AAAA,EAGhD;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA;AAAA,EAuBA,MAAM,IAAI,mBAAkE;AAC3E,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,QAAQ,YAAY;AACpE,aAAO,KAAK,KAAK,KAAK,GAAG,MAAM,EAAE,IAAI;AAAA,IACtC,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,IAAI,mBAAgE;AACzE,UAAM,EAAE,QAAQ,OAAO,QAAQ,MAAM,mBAAmB,IAAI;AAC5D,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,SAAS,iBAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AACjC,aAAO,MAAM,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AAC/D,eAAO,KAAK,KAAK,GAAG,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ,MAAM,KAAK,aAAa,OAAQ,CAAC;AAAA,MACpF,CAAC;AAAA,IACF;AAEA,UAAM,OAAO,MAAM,KAAK,OAAO,iBAAiB;AAEhD,WAAO,KAAK,aAAa,IAAI;AAAA,EAC9B;AAAA,EAES,aAAa,MAAe,aAAgC;AACpE,QAAI,aAAa;AAChB,aAAO,eAAgB,KAAkB,OAAO;AAAA,IACjD;AAEA,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAO;AAAA,IACR;AAEA,QAAI,KAAK,oBAAoB;AAC5B,aAAO,KAAK,mBAAmB,IAAmB;AAAA,IACnD;AAEA,WAAQ,KAAqB,IAAI,CAAC,QAAQ,aAAa,KAAK,QAAS,KAAK,KAAK,mBAAmB,CAAC;AAAA,EACpG;AAAA,EAEA,MAAM,IAAI,mBAAgE;AACzE,UAAM,EAAE,QAAQ,qBAAqB,OAAO,QAAQ,MAAM,mBAAmB,IAAI;AACjF,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,SAAS,iBAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AACjC,aAAO,MAAM,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AAC/D,eAAO,KAAK,KAAK,GAAG,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ,MAAM,QAAS,CAAC,CAAC;AAAA,MACpE,CAAC;AAAA,IACF;AAEA,UAAM,OAAO,MAAM,KAAK,OAAO,iBAAiB;AAEhD,QAAI,CAAC,KAAK,CAAC,GAAG;AACb,aAAO;AAAA,IACR;AAEA,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,aAAa,QAAS,KAAK,CAAC,GAAG,mBAAmB;AAAA,EAC1D;AAAA,EAES,aAAa,QAAiB,aAAgC;AACtE,QAAI,aAAa;AAChB,eAAS,eAAgB,OAAoB,OAAO,EAAE,CAAC;AAAA,IACxD;AAEA,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAO;AAAA,IACR;AAEA,QAAI,KAAK,oBAAoB;AAC5B,aAAO,KAAK,mBAAmB,CAAC,MAAmB,CAAC;AAAA,IACrD;AAEA,WAAO,aAAa,KAAK,QAAS,QAAqB,KAAK,mBAAmB;AAAA,EAChF;AAAA,EAEA,MAAM,OAAoC,mBAA2D;AACpG,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,QAAQ,YAAY;AACpE,aAAO,KAAK,KAAK,KAAK,GAAG,MAAM,EAAE,IAAI;AAAA,IACtC,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;", "names": ["builtQuery"]}