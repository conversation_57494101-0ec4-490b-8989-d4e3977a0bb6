{"version": 3, "sources": ["../../../src/gel-core/query-builders/refresh-materialized-view.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { GelDialect } from '~/gel-core/dialect.ts';\nimport type {\n\tGelPreparedQuery,\n\tGelQueryResultHKT,\n\tGelQueryResultKind,\n\tGelSession,\n\tPreparedQueryConfig,\n} from '~/gel-core/session.ts';\nimport type { GelMaterializedView } from '~/gel-core/view.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface GelRefreshMaterializedView<TQueryResult extends GelQueryResultHKT>\n\textends\n\t\tQueryPromise<GelQueryResultKind<TQueryResult, never>>,\n\t\tRunnableQuery<GelQueryResultKind<TQueryResult, never>, 'gel'>,\n\t\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'gel';\n\t\treadonly result: GelQueryResultKind<TQueryResult, never>;\n\t};\n}\n\nexport class GelRefreshMaterializedView<TQueryResult extends GelQueryResultHKT>\n\textends QueryPromise<GelQueryResultKind<TQueryResult, never>>\n\timplements RunnableQuery<GelQueryResultKind<TQueryResult, never>, 'gel'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'GelRefreshMaterializedView';\n\n\tprivate config: {\n\t\tview: GelMaterializedView;\n\t\tconcurrently?: boolean;\n\t\twithNoData?: boolean;\n\t};\n\n\tconstructor(\n\t\tview: GelMaterializedView,\n\t\tprivate session: GelSession,\n\t\tprivate dialect: GelDialect,\n\t) {\n\t\tsuper();\n\t\tthis.config = { view };\n\t}\n\n\tconcurrently(): this {\n\t\tif (this.config.withNoData !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.concurrently = true;\n\t\treturn this;\n\t}\n\n\twithNoData(): this {\n\t\tif (this.config.concurrently !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.withNoData = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildRefreshMaterializedViewQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): GelPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: GelQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), undefined, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): GelPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: GelQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn this._prepare(name);\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAU3B,SAAS,oBAAoB;AAG7B,SAAS,cAAc;AAehB,MAAM,mCACJ,aAET;AAAA,EASC,YACC,MACQ,SACA,SACP;AACD,UAAM;AAHE;AACA;AAGR,SAAK,SAAS,EAAE,KAAK;AAAA,EACtB;AAAA,EAfA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAeR,eAAqB;AACpB,QAAI,KAAK,OAAO,eAAe,QAAW;AACzC,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,SAAK,OAAO,eAAe;AAC3B,WAAO;AAAA,EACR;AAAA,EAEA,aAAmB;AAClB,QAAI,KAAK,OAAO,iBAAiB,QAAW;AAC3C,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,kCAAkC,KAAK,MAAM;AAAA,EAClE;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAIP;AACD,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAAa,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,QAAW,MAAM,IAAI;AAAA,IAC/F,CAAC;AAAA,EACF;AAAA,EAEA,QAAQ,MAIN;AACD,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAEA,UAAkD,CAAC,sBAAsB;AACxE,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,IACjD,CAAC;AAAA,EACF;AACD;", "names": []}