{"version": 3, "sources": ["../../src/gel-core/view.ts"], "sourcesContent": ["import type { BuildColumns } from '~/column-builder.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport { getTableColumns } from '~/utils.ts';\nimport type { RequireAtLeastOne } from '~/utils.ts';\nimport type { GelColumn, GelColumnBuilderBase } from './columns/common.ts';\nimport { QueryBuilder } from './query-builders/query-builder.ts';\nimport { gelTable } from './table.ts';\nimport { GelViewBase } from './view-base.ts';\nimport { GelViewConfig } from './view-common.ts';\n\nexport type ViewWithConfig = RequireAtLeastOne<{\n\tcheckOption: 'local' | 'cascaded';\n\tsecurityBarrier: boolean;\n\tsecurityInvoker: boolean;\n}>;\n\nexport class DefaultViewBuilderCore<TConfig extends { name: string; columns?: unknown }> {\n\tstatic readonly [entityKind]: string = 'GelDefaultViewBuilderCore';\n\n\tdeclare readonly _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t\tprotected schema: string | undefined,\n\t) {}\n\n\tprotected config: {\n\t\twith?: ViewWithConfig;\n\t} = {};\n\n\twith(config: ViewWithConfig): this {\n\t\tthis.config.with = config;\n\t\treturn this;\n\t}\n}\n\nexport class ViewBuilder<TName extends string = string> extends DefaultViewBuilderCore<{ name: TName }> {\n\tstatic override readonly [entityKind]: string = 'GelViewBuilder';\n\n\tas<TSelectedFields extends ColumnsSelection>(\n\t\tqb: TypedQueryBuilder<TSelectedFields> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelectedFields>),\n\t): GelViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'gel'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelectedFields>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\tconst aliasedSelection = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\treturn new Proxy(\n\t\t\tnew GelView({\n\t\t\t\tGelConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: aliasedSelection,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as GelViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'gel'>>;\n\t}\n}\n\nexport class ManualViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, GelColumnBuilderBase> = Record<string, GelColumnBuilderBase>,\n> extends DefaultViewBuilderCore<{ name: TName; columns: TColumns }> {\n\tstatic override readonly [entityKind]: string = 'GelManualViewBuilder';\n\n\tprivate columns: Record<string, GelColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t\tschema: string | undefined,\n\t) {\n\t\tsuper(name, schema);\n\t\tthis.columns = getTableColumns(gelTable(name, columns));\n\t}\n\n\texisting(): GelViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'gel'>> {\n\t\treturn new Proxy(\n\t\t\tnew GelView({\n\t\t\t\tGelConfig: undefined,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as GelViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'gel'>>;\n\t}\n\n\tas(query: SQL): GelViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'gel'>> {\n\t\treturn new Proxy(\n\t\t\tnew GelView({\n\t\t\t\tGelConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as GelViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'gel'>>;\n\t}\n}\n\nexport type GelMaterializedViewWithConfig = RequireAtLeastOne<{\n\tfillfactor: number;\n\ttoastTupleTarget: number;\n\tparallelWorkers: number;\n\tautovacuumEnabled: boolean;\n\tvacuumIndexCleanup: 'auto' | 'off' | 'on';\n\tvacuumTruncate: boolean;\n\tautovacuumVacuumThreshold: number;\n\tautovacuumVacuumScaleFactor: number;\n\tautovacuumVacuumCostDelay: number;\n\tautovacuumVacuumCostLimit: number;\n\tautovacuumFreezeMinAge: number;\n\tautovacuumFreezeMaxAge: number;\n\tautovacuumFreezeTableAge: number;\n\tautovacuumMultixactFreezeMinAge: number;\n\tautovacuumMultixactFreezeMaxAge: number;\n\tautovacuumMultixactFreezeTableAge: number;\n\tlogAutovacuumMinDuration: number;\n\tuserCatalogTable: boolean;\n}>;\n\nexport class MaterializedViewBuilderCore<TConfig extends { name: string; columns?: unknown }> {\n\tstatic readonly [entityKind]: string = 'GelMaterializedViewBuilderCore';\n\n\tdeclare _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t\tprotected schema: string | undefined,\n\t) {}\n\n\tprotected config: {\n\t\twith?: GelMaterializedViewWithConfig;\n\t\tusing?: string;\n\t\ttablespace?: string;\n\t\twithNoData?: boolean;\n\t} = {};\n\n\tusing(using: string): this {\n\t\tthis.config.using = using;\n\t\treturn this;\n\t}\n\n\twith(config: GelMaterializedViewWithConfig): this {\n\t\tthis.config.with = config;\n\t\treturn this;\n\t}\n\n\ttablespace(tablespace: string): this {\n\t\tthis.config.tablespace = tablespace;\n\t\treturn this;\n\t}\n\n\twithNoData(): this {\n\t\tthis.config.withNoData = true;\n\t\treturn this;\n\t}\n}\n\nexport class MaterializedViewBuilder<TName extends string = string>\n\textends MaterializedViewBuilderCore<{ name: TName }>\n{\n\tstatic override readonly [entityKind]: string = 'GelMaterializedViewBuilder';\n\n\tas<TSelectedFields extends ColumnsSelection>(\n\t\tqb: TypedQueryBuilder<TSelectedFields> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelectedFields>),\n\t): GelMaterializedViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'gel'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelectedFields>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\tconst aliasedSelection = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\treturn new Proxy(\n\t\t\tnew GelMaterializedView({\n\t\t\t\tGelConfig: {\n\t\t\t\t\twith: this.config.with,\n\t\t\t\t\tusing: this.config.using,\n\t\t\t\t\ttablespace: this.config.tablespace,\n\t\t\t\t\twithNoData: this.config.withNoData,\n\t\t\t\t},\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: aliasedSelection,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as GelMaterializedViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'gel'>>;\n\t}\n}\n\nexport class ManualMaterializedViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, GelColumnBuilderBase> = Record<string, GelColumnBuilderBase>,\n> extends MaterializedViewBuilderCore<{ name: TName; columns: TColumns }> {\n\tstatic override readonly [entityKind]: string = 'GelManualMaterializedViewBuilder';\n\n\tprivate columns: Record<string, GelColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t\tschema: string | undefined,\n\t) {\n\t\tsuper(name, schema);\n\t\tthis.columns = getTableColumns(gelTable(name, columns));\n\t}\n\n\texisting(): GelMaterializedViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'gel'>> {\n\t\treturn new Proxy(\n\t\t\tnew GelMaterializedView({\n\t\t\t\tGelConfig: {\n\t\t\t\t\ttablespace: this.config.tablespace,\n\t\t\t\t\tusing: this.config.using,\n\t\t\t\t\twith: this.config.with,\n\t\t\t\t\twithNoData: this.config.withNoData,\n\t\t\t\t},\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as GelMaterializedViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'gel'>>;\n\t}\n\n\tas(query: SQL): GelMaterializedViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'gel'>> {\n\t\treturn new Proxy(\n\t\t\tnew GelMaterializedView({\n\t\t\t\tGelConfig: {\n\t\t\t\t\ttablespace: this.config.tablespace,\n\t\t\t\t\tusing: this.config.using,\n\t\t\t\t\twith: this.config.with,\n\t\t\t\t\twithNoData: this.config.withNoData,\n\t\t\t\t},\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as GelMaterializedViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'gel'>>;\n\t}\n}\n\nexport class GelView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends GelViewBase<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'GelView';\n\n\t[GelViewConfig]: {\n\t\twith?: ViewWithConfig;\n\t} | undefined;\n\n\tconstructor({ GelConfig, config }: {\n\t\tGelConfig: {\n\t\t\twith?: ViewWithConfig;\n\t\t} | undefined;\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t\tif (GelConfig) {\n\t\t\tthis[GelViewConfig] = {\n\t\t\t\twith: GelConfig.with,\n\t\t\t};\n\t\t}\n\t}\n}\n\nexport type GelViewWithSelection<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = GelView<TName, TExisting, TSelectedFields> & TSelectedFields;\n\nexport const GelMaterializedViewConfig = Symbol.for('drizzle:GelMaterializedViewConfig');\n\nexport class GelMaterializedView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends GelViewBase<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'GelMaterializedView';\n\n\treadonly [GelMaterializedViewConfig]: {\n\t\treadonly with?: GelMaterializedViewWithConfig;\n\t\treadonly using?: string;\n\t\treadonly tablespace?: string;\n\t\treadonly withNoData?: boolean;\n\t} | undefined;\n\n\tconstructor({ GelConfig, config }: {\n\t\tGelConfig: {\n\t\t\twith: GelMaterializedViewWithConfig | undefined;\n\t\t\tusing: string | undefined;\n\t\t\ttablespace: string | undefined;\n\t\t\twithNoData: boolean | undefined;\n\t\t} | undefined;\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t\tthis[GelMaterializedViewConfig] = {\n\t\t\twith: GelConfig?.with,\n\t\t\tusing: GelConfig?.using,\n\t\t\ttablespace: GelConfig?.tablespace,\n\t\t\twithNoData: GelConfig?.withNoData,\n\t\t};\n\t}\n}\n\nexport type GelMaterializedViewWithSelection<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = GelMaterializedView<TName, TExisting, TSelectedFields> & TSelectedFields;\n\n/** @internal */\nexport function gelViewWithSchema(\n\tname: string,\n\tselection: Record<string, GelColumnBuilderBase> | undefined,\n\tschema: string | undefined,\n): ViewBuilder | ManualViewBuilder {\n\tif (selection) {\n\t\treturn new ManualViewBuilder(name, selection, schema);\n\t}\n\treturn new ViewBuilder(name, schema);\n}\n\n/** @internal */\nexport function gelMaterializedViewWithSchema(\n\tname: string,\n\tselection: Record<string, GelColumnBuilderBase> | undefined,\n\tschema: string | undefined,\n): MaterializedViewBuilder | ManualMaterializedViewBuilder {\n\tif (selection) {\n\t\treturn new ManualMaterializedViewBuilder(name, selection, schema);\n\t}\n\treturn new MaterializedViewBuilder(name, schema);\n}\n\n// TODO not implemented\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction gelView<TName extends string>(name: TName): ViewBuilder<TName>;\nfunction gelView<TName extends string, TColumns extends Record<string, GelColumnBuilderBase>>(\n\tname: TName,\n\tcolumns: TColumns,\n): ManualViewBuilder<TName, TColumns>;\nfunction gelView(name: string, columns?: Record<string, GelColumnBuilderBase>): ViewBuilder | ManualViewBuilder {\n\treturn gelViewWithSchema(name, columns, undefined);\n}\n\n// TODO not implemented\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction gelMaterializedView<TName extends string>(name: TName): MaterializedViewBuilder<TName>;\nfunction gelMaterializedView<TName extends string, TColumns extends Record<string, GelColumnBuilderBase>>(\n\tname: TName,\n\tcolumns: TColumns,\n): ManualMaterializedViewBuilder<TName, TColumns>;\nfunction gelMaterializedView(\n\tname: string,\n\tcolumns?: Record<string, GelColumnBuilderBase>,\n): MaterializedViewBuilder | ManualMaterializedViewBuilder {\n\treturn gelMaterializedViewWithSchema(name, columns, undefined);\n}\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction isGelView(obj: unknown): obj is GelView {\n\treturn is(obj, GelView);\n}\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction isGelMaterializedView(obj: unknown): obj is GelMaterializedView {\n\treturn is(obj, GelMaterializedView);\n}\n"], "mappings": "AACA,SAAS,YAAY,UAAU;AAG/B,SAAS,6BAA6B;AAEtC,SAAS,uBAAuB;AAGhC,SAAS,oBAAoB;AAC7B,SAAS,gBAAgB;AACzB,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAQvB,MAAM,uBAA4E;AAAA,EAQxF,YACW,MACA,QACT;AAFS;AACA;AAAA,EACR;AAAA,EAVH,QAAiB,UAAU,IAAY;AAAA,EAY7B,SAEN,CAAC;AAAA,EAEL,KAAK,QAA8B;AAClC,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AACD;AAEO,MAAM,oBAAmD,uBAAwC;AAAA,EACvG,QAA0B,UAAU,IAAY;AAAA,EAEhD,GACC,IACyF;AACzF,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,aAAa,CAAC;AAAA,IAC3B;AACA,UAAM,iBAAiB,IAAI,sBAAuC;AAAA,MACjE,OAAO,KAAK;AAAA,MACZ,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACtB,CAAC;AACD,UAAM,mBAAmB,IAAI,MAAM,GAAG,kBAAkB,GAAG,cAAc;AACzE,WAAO,IAAI;AAAA,MACV,IAAI,QAAQ;AAAA,QACX,WAAW,KAAK;AAAA,QAChB,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB;AAAA,UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,0BAGH,uBAA2D;AAAA,EACpE,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAER,YACC,MACA,SACA,QACC;AACD,UAAM,MAAM,MAAM;AAClB,SAAK,UAAU,gBAAgB,SAAS,MAAM,OAAO,CAAC;AAAA,EACvD;AAAA,EAEA,WAAoF;AACnF,WAAO,IAAI;AAAA,MACV,IAAI,QAAQ;AAAA,QACX,WAAW;AAAA,QACX,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO;AAAA,QACR;AAAA,MACD,CAAC;AAAA,MACD,IAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,GAAG,OAAsF;AACxF,WAAO,IAAI;AAAA,MACV,IAAI,QAAQ;AAAA,QACX,WAAW,KAAK;AAAA,QAChB,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO,MAAM,aAAa;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,MACD,IAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAuBO,MAAM,4BAAiF;AAAA,EAQ7F,YACW,MACA,QACT;AAFS;AACA;AAAA,EACR;AAAA,EAVH,QAAiB,UAAU,IAAY;AAAA,EAY7B,SAKN,CAAC;AAAA,EAEL,MAAM,OAAqB;AAC1B,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EAEA,KAAK,QAA6C;AACjD,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AAAA,EAEA,WAAW,YAA0B;AACpC,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,aAAmB;AAClB,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AACD;AAEO,MAAM,gCACJ,4BACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,GACC,IACqG;AACrG,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,aAAa,CAAC;AAAA,IAC3B;AACA,UAAM,iBAAiB,IAAI,sBAAuC;AAAA,MACjE,OAAO,KAAK;AAAA,MACZ,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACtB,CAAC;AACD,UAAM,mBAAmB,IAAI,MAAM,GAAG,kBAAkB,GAAG,cAAc;AACzE,WAAO,IAAI;AAAA,MACV,IAAI,oBAAoB;AAAA,QACvB,WAAW;AAAA,UACV,MAAM,KAAK,OAAO;AAAA,UAClB,OAAO,KAAK,OAAO;AAAA,UACnB,YAAY,KAAK,OAAO;AAAA,UACxB,YAAY,KAAK,OAAO;AAAA,QACzB;AAAA,QACA,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB;AAAA,UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,sCAGH,4BAAgE;AAAA,EACzE,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAER,YACC,MACA,SACA,QACC;AACD,UAAM,MAAM,MAAM;AAClB,SAAK,UAAU,gBAAgB,SAAS,MAAM,OAAO,CAAC;AAAA,EACvD;AAAA,EAEA,WAAgG;AAC/F,WAAO,IAAI;AAAA,MACV,IAAI,oBAAoB;AAAA,QACvB,WAAW;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,OAAO,KAAK,OAAO;AAAA,UACnB,MAAM,KAAK,OAAO;AAAA,UAClB,YAAY,KAAK,OAAO;AAAA,QACzB;AAAA,QACA,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO;AAAA,QACR;AAAA,MACD,CAAC;AAAA,MACD,IAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,GAAG,OAAkG;AACpG,WAAO,IAAI;AAAA,MACV,IAAI,oBAAoB;AAAA,QACvB,WAAW;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,OAAO,KAAK,OAAO;AAAA,UACnB,MAAM,KAAK,OAAO;AAAA,UAClB,YAAY,KAAK,OAAO;AAAA,QACzB;AAAA,QACA,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO,MAAM,aAAa;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,MACD,IAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAEO,MAAM,gBAIH,YAA+C;AAAA,EACxD,QAA0B,UAAU,IAAY;AAAA,EAEhD,CAAC,aAAa;AAAA,EAId,YAAY,EAAE,WAAW,OAAO,GAU7B;AACF,UAAM,MAAM;AACZ,QAAI,WAAW;AACd,WAAK,aAAa,IAAI;AAAA,QACrB,MAAM,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,EACD;AACD;AAQO,MAAM,4BAA4B,OAAO,IAAI,mCAAmC;AAEhF,MAAM,4BAIH,YAA+C;AAAA,EACxD,QAA0B,UAAU,IAAY;AAAA,EAEhD,CAAU,yBAAyB;AAAA,EAOnC,YAAY,EAAE,WAAW,OAAO,GAa7B;AACF,UAAM,MAAM;AACZ,SAAK,yBAAyB,IAAI;AAAA,MACjC,MAAM,WAAW;AAAA,MACjB,OAAO,WAAW;AAAA,MAClB,YAAY,WAAW;AAAA,MACvB,YAAY,WAAW;AAAA,IACxB;AAAA,EACD;AACD;AASO,SAAS,kBACf,MACA,WACA,QACkC;AAClC,MAAI,WAAW;AACd,WAAO,IAAI,kBAAkB,MAAM,WAAW,MAAM;AAAA,EACrD;AACA,SAAO,IAAI,YAAY,MAAM,MAAM;AACpC;AAGO,SAAS,8BACf,MACA,WACA,QAC0D;AAC1D,MAAI,WAAW;AACd,WAAO,IAAI,8BAA8B,MAAM,WAAW,MAAM;AAAA,EACjE;AACA,SAAO,IAAI,wBAAwB,MAAM,MAAM;AAChD;AASA,SAAS,QAAQ,MAAc,SAAiF;AAC/G,SAAO,kBAAkB,MAAM,SAAS,MAAS;AAClD;AASA,SAAS,oBACR,MACA,SAC0D;AAC1D,SAAO,8BAA8B,MAAM,SAAS,MAAS;AAC9D;AAEA,SAAS,UAAU,KAA8B;AAChD,SAAO,GAAG,KAAK,OAAO;AACvB;AAEA,SAAS,sBAAsB,KAA0C;AACxE,SAAO,GAAG,KAAK,mBAAmB;AACnC;", "names": []}